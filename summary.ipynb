{"cells": [{"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 最適化手法比較サマリー ===\n"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import numpy as np\n", "import os\n", "\n", "# 結果ファイルのパス\n", "mip_results_path = 'result/MIP_aggregate_results.csv'\n", "multistart_results_path = 'result/multi_start_aggregate_results.csv'\n", "\n", "print(\"=== 最適化手法比較サマリー ===\")"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MIP結果ファイルの読み込みエラー: 'utf-8' codec can't decode byte 0x83 in position 0: invalid start byte\n", "MIP結果ファイルを読み込みました（Shift_JIS）\n", "  ファイル名          目的関数値      計算時間\n", "0   D28  478517.458333  0.289293\n", "1   D36  174828.500000  0.100236\n", "2   D42  326701.750000  0.102479\n", "3    合計  980047.708333  0.492008\n"]}], "source": ["# CSVファイルを読み込み\n", "try:\n", "    mip_df = pd.read_csv(mip_results_path, encoding='utf-8')\n", "    print(\"MIP結果ファイルを読み込みました\")\n", "    print(mip_df)\n", "except Exception as e:\n", "    print(f\"MIP結果ファイルの読み込みエラー: {e}\")\n", "    # エンコーディングを変更して再試行\n", "    try:\n", "        mip_df = pd.read_csv(mip_results_path, encoding='shift_jis')\n", "        print(\"MIP結果ファイルを読み込みました（Shift_JIS）\")\n", "        print(mip_df)\n", "    except Exception as e2:\n", "        print(f\"MIP結果ファイルの読み込みに失敗: {e2}\")\n", "        mip_df = None"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "多スタートローカルサーチ結果ファイルを読み込みました\n", "  ファイル名         目的関数値      計算時間\n", "0   D28  6.406773e+05  0.028790\n", "1   D36  3.535219e+05  0.015684\n", "2   D42  3.017618e+05  0.020463\n", "3    合計  1.295961e+06  0.064937\n"]}], "source": ["try:\n", "    multistart_df = pd.read_csv(multistart_results_path, encoding='utf-8')\n", "    print(\"\\n多スタートローカルサーチ結果ファイルを読み込みました\")\n", "    print(multistart_df)\n", "except Exception as e:\n", "    print(f\"多スタートローカルサーチ結果ファイルの読み込みエラー: {e}\")\n", "    multistart_df = None"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 合計値比較 ===\n", "MIP - 目的関数値: 980047.71, 計算時間: 0.4920秒\n", "多スタートローカルサーチ - 目的関数値: 1295961.00, 計算時間: 0.0649秒\n", "\n", "=== 比較結果 ===\n", "目的関数値の差: 315913.29\n", "目的関数値の変化率: 32.23% (正の値は多スタートが悪い)\n", "計算時間の比率: 0.13 (多スタート/MIP)\n"]}], "source": ["# データの前処理と比較\n", "if mip_df is not None and multistart_df is not None:\n", "    # 合計行を抽出\n", "    mip_total = mip_df[mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    multistart_total = multistart_df[multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    \n", "    if len(mip_total) > 0 and len(multistart_total) > 0:\n", "        # 合計値を取得\n", "        mip_objective = float(mip_total.iloc[0, 1])  # 目的関数値\n", "        mip_time = float(mip_total.iloc[0, 2])       # 計算時間\n", "        \n", "        multistart_objective = float(multistart_total.iloc[0, 1])  # 目的関数値\n", "        multistart_time = float(multistart_total.iloc[0, 2])       # 計算時間\n", "        \n", "        print(f\"\\n=== 合計値比較 ===\")\n", "        print(f\"MIP - 目的関数値: {mip_objective:.2f}, 計算時間: {mip_time:.4f}秒\")\n", "        print(f\"多スタートローカルサーチ - 目的関数値: {multistart_objective:.2f}, 計算時間: {multistart_time:.4f}秒\")\n", "        \n", "        # 改善率の計算\n", "        objective_improvement = ((multistart_objective - mip_objective) / mip_objective) * 100\n", "        time_ratio = multistart_time / mip_time\n", "        \n", "        print(f\"\\n=== 比較結果 ===\")\n", "        print(f\"目的関数値の差: {multistart_objective - mip_objective:.2f}\")\n", "        print(f\"目的関数値の変化率: {objective_improvement:.2f}% (正の値は多スタートが悪い)\")\n", "        print(f\"計算時間の比率: {time_ratio:.2f} (多スタート/MIP)\")\n", "    else:\n", "        print(\"合計行が見つかりませんでした\")\n", "        mip_objective = mip_time = multistart_objective = multistart_time = None\n", "else:\n", "    print(\"データの読み込みに失敗したため、比較できません\")\n", "    mip_objective = mip_time = multistart_objective = multistart_time = None"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 個別データセット比較 ===\n", "MIP個別結果:\n", "  ファイル名          目的関数値      計算時間\n", "0   D28  478517.458333  0.289293\n", "1   D36  174828.500000  0.100236\n", "2   D42  326701.750000  0.102479\n", "\n", "多スタートローカルサーチ個別結果:\n", "  ファイル名          目的関数値      計算時間\n", "0   D28  640677.330833  0.028790\n", "1   D36  353521.916667  0.015684\n", "2   D42  301761.750000  0.020463\n"]}], "source": ["# 個別データセットの比較も行う\n", "if mip_df is not None and multistart_df is not None:\n", "    # 合計行以外のデータを取得\n", "    mip_individual = mip_df[~mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    multistart_individual = multistart_df[~multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    \n", "    print(f\"\\n=== 個別データセット比較 ===\")\n", "    print(f\"MIP個別結果:\")\n", "    print(mip_individual)\n", "    print(f\"\\n多スタートローカルサーチ個別結果:\")\n", "    print(multistart_individual)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "比較プロットを保存しました: result/optimization_comparison.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 詳細比較テーブル ===\n", "          手法      目的関数値 計算時間(秒)    相対性能  時間効率\n", "         MIP  980047.71  0.4920      基準    基準\n", "多スタートローカルサーチ 1295961.00  0.0649 +32.23% 0.13倍\n", "\n", "比較テーブルを保存しました: result/method_comparison_summary.csv\n"]}], "source": ["# 比較プロットの作成\n", "if mip_objective is not None and multistart_objective is not None:\n", "    # データの準備\n", "    methods = ['MIP', '多スタートローカルサーチ']\n", "    objectives = [mip_objective, multistart_objective]\n", "    times = [mip_time, multistart_time]\n", "    \n", "    # プロットの作成\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 目的関数値の比較\n", "    bars1 = ax1.bar(methods, objectives, color=['skyblue', 'lightcoral'], alpha=0.7)\n", "    ax1.set_title('目的関数値の比較', fontsize=14, fontweight='bold')\n", "    ax1.set_ylabel('目的関数値')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 値をバーの上に表示\n", "    for bar, value in zip(bars1, objectives):\n", "        height = bar.get_height()\n", "        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{value:.0f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 計算時間の比較\n", "    bars2 = ax2.bar(methods, times, color=['lightgreen', 'orange'], alpha=0.7)\n", "    ax2.set_title('計算時間の比較', fontsize=14, fontweight='bold')\n", "    ax2.set_ylabel('計算時間 (秒)')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 値をバーの上に表示\n", "    for bar, value in zip(bars2, times):\n", "        height = bar.get_height()\n", "        ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{value:.4f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # プロットを保存\n", "    output_path = 'result/optimization_comparison.png'\n", "    plt.savefig(output_path, dpi=300, bbox_inches='tight')\n", "    print(f\"\\n比較プロットを保存しました: {output_path}\")\n", "    \n", "    plt.show()\n", "    \n", "    # 詳細比較テーブルの作成\n", "    comparison_data = {\n", "        '手法': ['MIP', '多スタートローカルサーチ'],\n", "        '目的関数値': [f'{mip_objective:.2f}', f'{multistart_objective:.2f}'],\n", "        '計算時間(秒)': [f'{mip_time:.4f}', f'{multistart_time:.4f}'],\n", "        '相対性能': ['基準', f'{objective_improvement:+.2f}%'],\n", "        '時間効率': ['基準', f'{time_ratio:.2f}倍']\n", "    }\n", "    \n", "    comparison_df = pd.DataFrame(comparison_data)\n", "    print(f\"\\n=== 詳細比較テーブル ===\")\n", "    print(comparison_df.to_string(index=False))\n", "    \n", "    # 比較テーブルをCSVとして保存\n", "    comparison_csv_path = 'result/method_comparison_summary.csv'\n", "    comparison_df.to_csv(comparison_csv_path, index=False, encoding='utf-8')\n", "    print(f\"\\n比較テーブルを保存しました: {comparison_csv_path}\")\n", "    \n", "else:\n", "    print(\"データが不足しているため、プロットを作成できません\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}