{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 最適化手法比較サマリー\n", "\n", "このノートブックでは、混合整数計画法（MIP）と多スタートローカルサーチの結果を比較します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import numpy as np\n", "import os\n", "\n", "# 結果ファイルのパス\n", "mip_results_path = 'result/MIP_aggregate_results.csv'\n", "multistart_results_path = 'result/multi_start_aggregate_results.csv'\n", "\n", "print(\"=== 最適化手法比較サマリー ===\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# CSVファイルを読み込み\n", "try:\n", "    mip_df = pd.read_csv(mip_results_path, encoding='utf-8')\n", "    print(\"MIP結果ファイルを読み込みました\")\n", "    print(mip_df)\n", "except Exception as e:\n", "    print(f\"MIP結果ファイルの読み込みエラー: {e}\")\n", "    # エンコーディングを変更して再試行\n", "    try:\n", "        mip_df = pd.read_csv(mip_results_path, encoding='shift_jis')\n", "        print(\"MIP結果ファイルを読み込みました（Shift_JIS）\")\n", "        print(mip_df)\n", "    except Exception as e2:\n", "        print(f\"MIP結果ファイルの読み込みに失敗: {e2}\")\n", "        mip_df = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["try:\n", "    multistart_df = pd.read_csv(multistart_results_path, encoding='utf-8')\n", "    print(\"\\n多スタートローカルサーチ結果ファイルを読み込みました\")\n", "    print(multistart_df)\n", "except Exception as e:\n", "    print(f\"多スタートローカルサーチ結果ファイルの読み込みエラー: {e}\")\n", "    multistart_df = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# データの前処理と比較\n", "if mip_df is not None and multistart_df is not None:\n", "    # 合計行を抽出\n", "    mip_total = mip_df[mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    multistart_total = multistart_df[multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    \n", "    if len(mip_total) > 0 and len(multistart_total) > 0:\n", "        # 合計値を取得\n", "        mip_objective = float(mip_total.iloc[0, 1])  # 目的関数値\n", "        mip_time = float(mip_total.iloc[0, 2])       # 計算時間\n", "        \n", "        multistart_objective = float(multistart_total.iloc[0, 1])  # 目的関数値\n", "        multistart_time = float(multistart_total.iloc[0, 2])       # 計算時間\n", "        \n", "        print(f\"\\n=== 合計値比較 ===\")\n", "        print(f\"MIP - 目的関数値: {mip_objective:.2f}, 計算時間: {mip_time:.4f}秒\")\n", "        print(f\"多スタートローカルサーチ - 目的関数値: {multistart_objective:.2f}, 計算時間: {multistart_time:.4f}秒\")\n", "        \n", "        # 改善率の計算\n", "        objective_improvement = ((multistart_objective - mip_objective) / mip_objective) * 100\n", "        time_ratio = multistart_time / mip_time\n", "        \n", "        print(f\"\\n=== 比較結果 ===\")\n", "        print(f\"目的関数値の差: {multistart_objective - mip_objective:.2f}\")\n", "        print(f\"目的関数値の変化率: {objective_improvement:.2f}% (正の値は多スタートが悪い)\")\n", "        print(f\"計算時間の比率: {time_ratio:.2f} (多スタート/MIP)\")\n", "    else:\n", "        print(\"合計行が見つかりませんでした\")\n", "        mip_objective = mip_time = multistart_objective = multistart_time = None\n", "else:\n", "    print(\"データの読み込みに失敗したため、比較できません\")\n", "    mip_objective = mip_time = multistart_objective = multistart_time = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 個別データセットの比較も行う\n", "if mip_df is not None and multistart_df is not None:\n", "    # 合計行以外のデータを取得\n", "    mip_individual = mip_df[~mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    multistart_individual = multistart_df[~multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    \n", "    print(f\"\\n=== 個別データセット比較 ===\")\n", "    print(f\"MIP個別結果:\")\n", "    print(mip_individual)\n", "    print(f\"\\n多スタートローカルサーチ個別結果:\")\n", "    print(multistart_individual)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 比較プロットの作成\n", "if mip_objective is not None and multistart_objective is not None:\n", "    # データの準備\n", "    methods = ['MIP', '多スタートローカルサーチ']\n", "    objectives = [mip_objective, multistart_objective]\n", "    times = [mip_time, multistart_time]\n", "    \n", "    # プロットの作成\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 目的関数値の比較\n", "    bars1 = ax1.bar(methods, objectives, color=['skyblue', 'lightcoral'], alpha=0.7)\n", "    ax1.set_title('目的関数値の比較', fontsize=14, fontweight='bold')\n", "    ax1.set_ylabel('目的関数値')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 値をバーの上に表示\n", "    for bar, value in zip(bars1, objectives):\n", "        height = bar.get_height()\n", "        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{value:.0f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 計算時間の比較\n", "    bars2 = ax2.bar(methods, times, color=['lightgreen', 'orange'], alpha=0.7)\n", "    ax2.set_title('計算時間の比較', fontsize=14, fontweight='bold')\n", "    ax2.set_ylabel('計算時間 (秒)')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 値をバーの上に表示\n", "    for bar, value in zip(bars2, times):\n", "        height = bar.get_height()\n", "        ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{value:.4f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # プロットを保存\n", "    output_path = 'result/optimization_comparison.png'\n", "    plt.savefig(output_path, dpi=300, bbox_inches='tight')\n", "    print(f\"\\n比較プロットを保存しました: {output_path}\")\n", "    \n", "    plt.show()\n", "else:\n", "    print(\"データが不足しているため、プロットを作成できません\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}