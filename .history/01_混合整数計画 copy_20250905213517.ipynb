{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "f57a500f",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "\n",
      "=== 初期在庫水準の調整アルゴリズム開始 ===\n",
      "--- 調整イテレーション 1 ---\n",
      "  今回の調整量: [1200, 1000, 500, 50]\n",
      "  更新後の初期在庫量: [3263, 2273, 1767, 185]\n",
      "--- 調整イテレーション 2 ---\n",
      "  今回の調整量: [1200, 1000, 500, 50]\n",
      "  更新後の初期在庫量: [2063, 1273, 1267, 135]\n",
      "--- 調整イテレーション 3 ---\n",
      "  今回の調整量: [1200, 1000, 500, 50]\n",
      "  更新後の初期在庫量: [863, 273, 767, 85]\n",
      "--- 調整イテレーション 4 ---\n",
      "  今回の調整量: [863, 273, 500, 50]\n",
      "  更新後の初期在庫量: [0, 0, 267, 35]\n",
      "--- 調整イテレーション 5 ---\n",
      "  今回の調整量: [0, 0, 267, 35]\n",
      "  更新後の初期在庫量: [0, 0, 0, 0]\n",
      "--- 最大反復回数に到達しました。---\n",
      "=== 混合整数計画法 スケジューリング ===\n",
      "Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.6.0 24G90)\n",
      "\n",
      "CPU model: Apple M1\n",
      "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n",
      "\n",
      "Optimize a model with 260 rows, 360 columns and 852 nonzeros\n",
      "Model fingerprint: 0xa5915a06\n",
      "Variable types: 200 continuous, 160 integer (0 binary)\n",
      "Coefficient statistics:\n",
      "  Matrix range     [3e-01, 1e+06]\n",
      "  Objective range  [7e+01, 5e+02]\n",
      "  Bounds range     [1e+00, 1e+00]\n",
      "  RHS range        [5e+01, 1e+03]\n",
      "Found heuristic solution: objective 2.887500e+08\n",
      "Presolve removed 60 rows and 4 columns\n",
      "Presolve time: 0.00s\n",
      "Presolved: 200 rows, 356 columns, 768 nonzeros\n",
      "Variable types: 156 continuous, 200 integer (80 binary)\n",
      "\n",
      "Root relaxation: objective 1.710259e+03, 160 iterations, 0.00 seconds (0.00 work units)\n",
      "\n",
      "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n",
      " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n",
      "\n",
      "     0     0 1710.25869    0  100 2.8875e+08 1710.25869   100%     -    0s\n",
      "H    0     0                    7581710.0000 1710.25869   100%     -    0s\n",
      "H    0     0                    10400.000000 1710.25869  83.6%     -    0s\n",
      "\n",
      "Cutting planes:\n",
      "  StrongCG: 19\n",
      "\n",
      "Explored 1 nodes (160 simplex iterations) in 0.02 seconds (0.00 work units)\n",
      "Thread count was 8 (of 8 available processors)\n",
      "\n",
      "Solution count 3: 10400 7.58171e+06 2.8875e+08 \n",
      "\n",
      "Optimal solution found (tolerance 1.00e-04)\n",
      "Best objective 1.040000000000e+04, best bound 1.040000000000e+04, gap 0.0000%\n",
      "Gurobi status= 2\n",
      "ステータス: Optimal\n",
      "総コスト: 10400.0\n",
      "\n",
      "=== 最適化結果 ===\n",
      "最良個体の総コスト: 10400.00\n",
      "\n",
      "=== 結果のプロット ===\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "/var/folders/_p/gpx29nx95pn__7720hyny3sm0000gn/T/ipykernel_18802/1044550347.py:302: UserWarning: Attempting to set identical low and high ylims makes transformation singular; automatically expanding.\n",
      "  axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n",
      "/var/folders/_p/gpx29nx95pn__7720hyny3sm0000gn/T/ipykernel_18802/1044550347.py:329: UserWarning: Attempting to set identical low and high ylims makes transformation singular; automatically expanding.\n",
      "  axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n"
     ]
    },
    {
     "data": {
      "image/png": "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",
      "text/plain": [
       "<Figure size 1600x1200 with 4 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "時間制約違反: 0 期間\n"
     ]
    }
   ],
   "source": [
    "import pandas as pd\n",
    "import pulp\n",
    "import csv\n",
    "import random\n",
    "import matplotlib.pyplot as plt\n",
    "import japanize_matplotlib\n",
    "import copy\n",
    "\n",
    "# グローバル変数\n",
    "品番リスト = []\n",
    "出荷数リスト = []\n",
    "収容数リスト = []\n",
    "サイクルタイムリスト = []\n",
    "込め数リスト = []\n",
    "初期在庫量リスト = []\n",
    "\n",
    "# コストとペナルティの係数\n",
    "在庫コスト単価 = 180\n",
    "残業コスト単価 = 66.7\n",
    "段替えコスト単価 = 130\n",
    "出荷遅れコスト単価 = 500\n",
    "\n",
    "定時 = 8 * 60 * 2\n",
    "最大残業時間 = 2 * 60 * 2\n",
    "段替え時間 = 30\n",
    "\n",
    "def read_csv(file_path):\n",
    "    \"\"\"CSVファイルを読み込む関数\"\"\"\n",
    "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n",
    "\n",
    "    with open(file_path, 'r', encoding='utf-8') as file:\n",
    "        reader = csv.reader(file)\n",
    "        header = next(reader)\n",
    "        \n",
    "        品番リスト = []\n",
    "        出荷数リスト = []\n",
    "        収容数リスト = []\n",
    "        サイクルタイムリスト = []\n",
    "        込め数リスト = []\n",
    "        \n",
    "        rows = list(reader)\n",
    "        for row in rows:\n",
    "            if len(row) == 0:\n",
    "                continue\n",
    "            品番リスト.append(row[header.index(\"part_number\")])\n",
    "            出荷数リスト.append(int(row[header.index(\"shipment\")]))\n",
    "            収容数リスト.append(int(row[header.index(\"capacity\")]))\n",
    "            \n",
    "            cycle_time_per_unit = float(row[header.index(\"cycle_time\")]) / 60\n",
    "            サイクルタイムリスト.append(cycle_time_per_unit)\n",
    "            \n",
    "            込め数リスト.append(int(row[header.index(\"cabity\")]))\n",
    "    \n",
    "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n",
    "        初期在庫量リスト = []\n",
    "        for shipment in 出荷数リスト:\n",
    "            random_inventory = random.randint(shipment * 3, shipment * 5)\n",
    "            初期在庫量リスト.append(random_inventory)\n",
    "            \n",
    "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n",
    "\n",
    "def solve_mip(initial_inventory_list_arg):\n",
    "    \"\"\"PuLPを用いてMIPを解く関数\"\"\"\n",
    "    \n",
    "    # モデルの定義\n",
    "    model = pulp.LpProblem(\"ProductionScheduling\", pulp.LpMinimize)\n",
    "    \n",
    "    # インデックスの定義\n",
    "    品目 = range(品番数)\n",
    "    期間_index = range(期間)\n",
    "\n",
    "    # 決定変数\n",
    "    Production = pulp.LpVariable.dicts(\"Production\", (品目, 期間_index), lowBound=0, cat='Integer')\n",
    "    IsProduced = pulp.LpVariable.dicts(\"IsProduced\", (品目, 期間_index), cat='Binary')\n",
    "    Inventory = pulp.LpVariable.dicts(\"Inventory\", (品目, 期間_index), lowBound=0, cat='Continuous')\n",
    "    Shortage = pulp.LpVariable.dicts(\"Shortage\", (品目, 期間_index), lowBound=0, cat='Continuous')\n",
    "    WorkTime = pulp.LpVariable.dicts(\"WorkTime\", 期間_index, lowBound=0, cat='Continuous')\n",
    "    Overtime = pulp.LpVariable.dicts(\"Overtime\", 期間_index, lowBound=0, cat='Continuous')\n",
    "\n",
    "    # 目的関数\n",
    "    total_cost = pulp.lpSum(\n",
    "        在庫コスト単価 * Inventory[i][t] for i in 品目 for t in 期間_index\n",
    "    ) + pulp.lpSum(\n",
    "        残業コスト単価 * Overtime[t] for t in 期間_index\n",
    "    ) + pulp.lpSum(\n",
    "        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index\n",
    "    ) + pulp.lpSum(\n",
    "        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index\n",
    "    )\n",
    "    \n",
    "    model += total_cost, \"Total Cost\"\n",
    "\n",
    "    # 制約条件\n",
    "    bigM = 1000000\n",
    "\n",
    "    for i in 品目:\n",
    "        for t in 期間_index:\n",
    "            if t == 0:\n",
    "                # 初期在庫リストを使用\n",
    "                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]\n",
    "            else:\n",
    "                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]\n",
    "            \n",
    "            model += Production[i][t] <= bigM * IsProduced[i][t]\n",
    "\n",
    "    for t in 期間_index:\n",
    "        model += WorkTime[t] == pulp.lpSum(\n",
    "            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]\n",
    "            for i in 品目\n",
    "        )\n",
    "        \n",
    "        model += WorkTime[t] <= 定時 + Overtime[t]\n",
    "        model += WorkTime[t] <= 定時 + 最大残業時間\n",
    "        model += Overtime[t] >= WorkTime[t] - 定時\n",
    "        model += Overtime[t] >= 0\n",
    "\n",
    "    # Solverの設定\n",
    "    solver = pulp.GUROBI(msg=True)\n",
    "    \n",
    "    # 最適化の実行\n",
    "    model.solve(solver)\n",
    "    \n",
    "    print(\"ステータス:\", pulp.LpStatus[model.status])\n",
    "    if pulp.LpStatus[model.status] == 'Optimal':\n",
    "        print(\"総コスト:\", pulp.value(model.objective))\n",
    "\n",
    "        production_schedule = [[0] * 期間 for _ in range(品番数)]\n",
    "        for i in 品目:\n",
    "            for t in 期間_index:\n",
    "                production_schedule[i][t] = pulp.value(Production[i][t])\n",
    "\n",
    "        return production_schedule, pulp.value(model.objective)\n",
    "    \n",
    "    return None, None\n",
    "\n",
    "\n",
    "def simulate_production_schedule(initial_inventory, 期間=20):\n",
    "    \"\"\"生産スケジュールをシミュレートする関数（MIP用の簡易版）\"\"\"\n",
    "    品番数 = len(initial_inventory)\n",
    "    inventory = initial_inventory[:]\n",
    "    inventory_history = [[] for _ in range(品番数)]\n",
    "    \n",
    "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n",
    "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n",
    "    max_daily_work_time = daily_regular_time + max_daily_overtime\n",
    "    \n",
    "    for t in range(期間):\n",
    "        daily_production_time = 0\n",
    "        daily_setup_count = 0\n",
    "        \n",
    "        # 各品番の需要を処理\n",
    "        for i in range(品番数):\n",
    "            demand = 出荷数リスト[i]\n",
    "            inventory[i] -= demand\n",
    "            \n",
    "            # 在庫が不足する場合は生産\n",
    "            if inventory[i] < 0:\n",
    "                shortage = abs(inventory[i])\n",
    "                # 生産量を決定（不足分を補う）\n",
    "                production = shortage\n",
    "                \n",
    "                # 生産時間を計算\n",
    "                if production > 0:\n",
    "                    daily_setup_count += 1\n",
    "                    setup_time = 30  # 段替え時間\n",
    "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n",
    "                    daily_production_time += production_time + setup_time\n",
    "                \n",
    "                inventory[i] += production\n",
    "            \n",
    "            # 在庫履歴に記録\n",
    "            inventory_history[i].append(max(0, inventory[i]))\n",
    "        \n",
    "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n",
    "        if daily_production_time > max_daily_work_time:\n",
    "            # 簡易的な調整：超過分を比例配分で削減\n",
    "            reduction_factor = max_daily_work_time / daily_production_time\n",
    "            for i in range(品番数):\n",
    "                if inventory[i] > 0:\n",
    "                    # 生産量を削減し、在庫を調整\n",
    "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n",
    "                    reduced_production = current_production * reduction_factor\n",
    "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n",
    "                    inventory[i] = max(0, inventory[i])\n",
    "                    inventory_history[i][-1] = inventory[i]\n",
    "    \n",
    "    return inventory_history\n",
    "\n",
    "def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):\n",
    "    \"\"\"初期在庫を更新する関数（optimize_initial_inventoryと一致させた版）\"\"\"\n",
    "    \n",
    "    品番数 = len(初期在庫量リスト)\n",
    "    s = 初期在庫量リスト[:]\n",
    "    \n",
    "    # h / (h+c) - optimize_initial_inventoryと同じ計算方法\n",
    "    prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n",
    "    \n",
    "    print(f\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n",
    "    \n",
    "    for iteration in range(max_iterations):\n",
    "        print(f\"--- 調整イテレーション {iteration + 1} ---\")\n",
    "        \n",
    "        # 各在庫点について在庫量の分布を求める\n",
    "        inventory_distributions = [[] for _ in range(品番数)]\n",
    "        for _ in range(num_simulations):\n",
    "            inventory_history = simulate_production_schedule(s)\n",
    "            for i in range(品番数):\n",
    "                inventory_distributions[i].extend(inventory_history[i])\n",
    "        \n",
    "        adjustments = [0] * 品番数\n",
    "        \n",
    "        # 各在庫点について在庫量の最適調整量r^*を求める\n",
    "        for i in range(品番数):\n",
    "            if not inventory_distributions[i]:\n",
    "                continue\n",
    "            \n",
    "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n",
    "            cumulative_distribution = inventory_counts.cumsum()\n",
    "            \n",
    "            # sum_{x <= r-1} f(x) <= h / (h + c) - optimize_initial_inventoryと同じロジック\n",
    "            best_r = 0\n",
    "            for r in cumulative_distribution.index:\n",
    "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n",
    "                if prob_at_r_minus_1 <= prob_target:\n",
    "                    best_r = r\n",
    "                else:\n",
    "                    break\n",
    "            \n",
    "            adjustments[i] = s[i] - best_r\n",
    "            \n",
    "        print(f\"  今回の調整量: {adjustments}\")\n",
    "        \n",
    "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n",
    "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n",
    "        \n",
    "        # 終了条件のチェック - optimize_initial_inventoryと同じ条件\n",
    "        if all(abs(adj) < 1 for adj in adjustments):\n",
    "            print(\"--- アルゴリズムが収束しました。---\")\n",
    "            return s\n",
    "            \n",
    "        s = new_s\n",
    "        print(f\"  更新後の初期在庫量: {s}\")\n",
    "        \n",
    "    print(\"--- 最大反復回数に到達しました。---\")\n",
    "    return s\n",
    "\n",
    "def plot_results(best_individual, initial_inventory, save_path=None):\n",
    "    \"\"\"結果をプロットする関数\"\"\"\n",
    "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n",
    "    \n",
    "    print(\"\\n=== 結果のプロット ===\")\n",
    "    \n",
    "    total_inventory_per_period = []\n",
    "    total_production_time_per_period = []\n",
    "    total_setup_times_per_period = []\n",
    "    total_shipment_delay_per_period = []\n",
    "\n",
    "    inventory = initial_inventory[:]\n",
    "    max_daily_work_time = (8 + 2) * 60 * 2\n",
    "    daily_regular_time = 8 * 60 * 2\n",
    "    \n",
    "    for t in range(期間):\n",
    "        daily_inventory = 0\n",
    "        daily_production_time = 0\n",
    "        daily_setup_times = 0\n",
    "        daily_shipment_delay = 0\n",
    "        \n",
    "        for i in range(品番数):\n",
    "            production = best_individual[i][t]\n",
    "            \n",
    "            if production > 0:\n",
    "                daily_setup_times += 1\n",
    "                setup_time = 30\n",
    "            else:\n",
    "                setup_time = 0\n",
    "\n",
    "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n",
    "            daily_production_time += production_time + setup_time\n",
    "            \n",
    "            inventory[i] += production - 出荷数リスト[i]\n",
    "\n",
    "            if inventory[i] < 0:\n",
    "                daily_shipment_delay += abs(inventory[i])\n",
    "                inventory[i] = 0\n",
    "\n",
    "            daily_inventory += inventory[i]\n",
    "            \n",
    "        total_inventory_per_period.append(daily_inventory)\n",
    "        total_production_time_per_period.append(daily_production_time)\n",
    "        total_setup_times_per_period.append(daily_setup_times)\n",
    "        total_shipment_delay_per_period.append(daily_shipment_delay)\n",
    "\n",
    "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n",
    "    periods = list(range(1, 期間 + 1))\n",
    "\n",
    "    # 1. 各期間の総在庫量\n",
    "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n",
    "    axes[0, 0].set_title('各期間の総在庫量')\n",
    "    axes[0, 0].set_xlabel('期間')\n",
    "    axes[0, 0].set_ylabel('総在庫量 (個)')\n",
    "    axes[0, 0].grid(True, alpha=0.3)\n",
    "    if total_inventory_per_period:\n",
    "        axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n",\n",
    "\n",
    "    # 2. 各期間の総生産時間＋制限ライン\n",
    "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n",
    "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n",
    "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n",
    "    axes[0, 1].set_title('各期間の総生産時間')\n",
    "    axes[0, 1].set_xlabel('期間')\n",
    "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n",
    "    axes[0, 1].grid(True, alpha=0.3)\n",
    "    axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n",
    "    axes[0, 1].legend()\n",
    "\n",
    "    # 3. 各期間の総段替え回数\n",
    "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n",
    "    axes[1, 0].set_title('各期間の総段替え回数')\n",
    "    axes[1, 0].set_xlabel('期間')\n",
    "    axes[1, 0].set_ylabel('総段替え回数（回）')\n",
    "    axes[1, 0].grid(True, alpha=0.3)\n",
    "    axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n",
    "\n",
    "    # 4. 各期間の総出荷遅れ量\n",
    "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n",
    "    axes[1, 1].set_title('各期間の総出荷遅れ量')\n",
    "    axes[1, 1].set_xlabel('期間')\n",
    "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n",
    "    axes[1, 1].grid(True, alpha=0.3)\n",
    "    axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n",
    "\n",
    "    plt.tight_layout()\n",
    "    \n",
    "    if save_path:\n",
    "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n",
    "        print(f\"プロットを保存: {save_path}\")\n",
    "    else:\n",
    "        plt.show()\n",
    "\n",
    "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n",
    "    print(f\"時間制約違反: {time_violations} 期間\")\n",
    "    \n",
    "import os\n",
    "    import time\n",
    "    \n",
    "    def process_single_file(file_path):\n",
    "        \"\"\"単一のCSVファイルを処理する関数\"\"\"\n",
    "        global 品番数, 期間, 初期在庫量リスト\n",
    "        \n",
    "        print(f\"\\n=== Processing {file_path} ===\")\n",
    "        \n",
    "        # CSVファイルを読み込み\n",
    "        品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)\n",
    "        品番数 = len(品番リスト)\n",
    "        期間 = 20\n",
    "        在庫コスト単価 = 180\n",
    "        出荷遅れコスト単価 = 500\n",
    "        \n",
    "        # 初期在庫量を調整\n",
    "        adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5)\n",
    "        初期在庫量リスト = adjusted_initial_inventory\n",
    "        \n",
    "        print(\"=== 混合整数計画法 スケジューリング ===\")\n",
    "        \n",
    "        # 計算時間を測定\n",
    "        start_time = time.time()\n",
    "        best_solution, best_cost = solve_mip(初期在庫量リスト)\n",
    "        calculation_time = time.time() - start_time\n",
    "        \n",
    "        if best_solution:\n",
    "            print(f\"\\n=== 最適化結果 ===\")\n",
    "            print(f\"最良個体の総コスト: {best_cost:.2f}\")\n",
    "            print(f\"計算時間: {calculation_time:.2f}秒\")\n",
    "            \n",
    "            # 結果をDataFrameに変換\n",
    "            result_df = pd.DataFrame(best_solution, \n",
    "                                   index=[f\"品番_{i+1}\" for i in range(品番数)],\n",
    "                                   columns=[f\"期間_{t+1}\" for t in range(期間)])\n",
    "            \n",
    "            # ファイル名から拡張子を除去\n",
    "            base_name = os.path.splitext(os.path.basename(file_path))[0]\n",
    "            \n",
    "            # 結果をCSVとして保存\n",
    "            result_csv_path = f\"results_{base_name}.csv\"\n",
    "            result_df.to_csv(result_csv_path)\n",
    "            print(f\"結果をCSVファイルに保存: {result_csv_path}\")\n",
    "            \n",
    "            # プロットを作成して保存\n",
    "            plot_path = f\"results_{base_name}.png\"\n",
    "            plot_results(best_solution, 初期在庫量リスト, save_path=plot_path)\n",
    "            \n",
    "            return best_cost, calculation_time, base_name\n",
    "        else:\n",
    "            print(\"\\n解が見つかりませんでした\")\n",
    "            return None, None, None\n",
    "    \n",
    "    def main():\n",
    "        \"\"\"メイン実行関数 - 全CSVファイルを処理\"\"\"\n",
    "        data_folder = \"data\"\n",
    "        \n",
    "        # dataフォルダ内のすべてのCSVファイルを取得\n",
    "        csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]\n",
    "        \n",
    "        if not csv_files:\n",
    "            print(\"dataフォルダにCSVファイルが見つかりません\")\n",
    "            return\n",
    "        \n",
    "        print(f\"見つかったCSVファイル: {csv_files}\")\n",
    "        \n",
    "        # 結果を格納するリスト\n",
    "        all_results = []\n",
    "        total_objective_value = 0\n",
    "        total_calculation_time = 0\n",
    "        \n",
    "        # 各CSVファイルを処理\n",
    "        for csv_file in csv_files:\n",
    "            file_path = os.path.join(data_folder, csv_file)\n",
    "            objective_value, calc_time, file_name = process_single_file(file_path)\n",
    "            \n",
    "            if objective_value is not None:\n",
    "                all_results.append({\n",
    "                    'ファイル名': file_name,\n",
    "                    '目的関数値': objective_value,\n",
    "                    '計算時間': calc_time\n",
    "                })\n",
    "                total_objective_value += objective_value\n",
    "                total_calculation_time += calc_time\n",
    "        \n",
    "        # 集計結果をDataFrameに変換\n",
    "        summary_df = pd.DataFrame(all_results)\n",
    "        \n",
    "        # 合計行を追加\n",
    "        summary_row = pd.DataFrame({\n",
    "            'ファイル名': ['合計'],\n",
    "            '目的関数値': [total_objective_value],\n",
    "            '計算時間': [total_calculation_time]\n",
    "        })\n",
    "        summary_df = pd.concat([summary_df, summary_row], ignore_index=True)\n",
    "        \n",
    "        # 集計結果をCSVとして保存\n",
    "        summary_csv_path = \"aggregate_results.csv\"\n",
    "        summary_df.to_csv(summary_csv_path, index=False)\n",
    "        print(f\"\\n集計結果をCSVファイルに保存: {summary_csv_path}\")\n",
    "        \n",
    "        # 集計結果のプロットを作成\n",
    "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n",
    "        \n",
    "        # 目的関数値のプロット（合計を除く）\n",
    "        individual_results = summary_df[summary_df['ファイル名'] != '合計']\n",
    "        ax1.bar(individual_results['ファイル名'], individual_results['目的関数値'], \n",
    "                color='skyblue', alpha=0.7)\n",
    "        ax1.set_title('各データセットの目的関数値')\n",
    "        ax1.set_xlabel('データセット')\n",
    "        ax1.set_ylabel('目的関数値')\n",
    "        ax1.tick_params(axis='x', rotation=45)\n",
    "        ax1.grid(True, alpha=0.3)\n",
    "        \n",
    "        # 計算時間のプロット（合計を除く）\n",
    "        ax2.bar(individual_results['ファイル名'], individual_results['計算時間'], \n",
    "                color='lightgreen', alpha=0.7)\n",
    "        ax2.set_title('各データセットの計算時間')\n",
    "        ax2.set_xlabel('データセット')\n",
    "        ax2.set_ylabel('計算時間 (秒)')\n",
    "        ax2.tick_params(axis='x', rotation=45)\n",
    "        ax2.grid(True, alpha=0.3)\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        \n",
    "        # 集計プロットを保存\n",
    "        aggregate_plot_path = \"aggregate_results.png\"\n",
    "        plt.savefig(aggregate_plot_path, dpi=300, bbox_inches='tight')\n",
    "        plt.show()\n",
    "        print(f\"集計プロットを画像ファイルに保存: {aggregate_plot_path}\")\n",
    "        \n",
    "        # 結果の要約を表示\n",
    "        print(f\"\\n=== 全体の集計結果 ===\")\n",
    "        print(f\"処理したファイル数: {len(all_results)}\")\n",
    "        print(f\"総目的関数値: {total_objective_value:.2f}\")\n",
    "        print(f\"総計算時間: {total_calculation_time:.2f}秒\")\n",
    "        print(f\"平均目的関数値: {total_objective_value/len(all_results):.2f}\")\n",
    "        print(f\"平均計算時間: {total_calculation_time/len(all_results):.2f}秒\")\n",
    "        \n",
    "if __name__ == \"__main__\":\n",
    "    main()"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "d76b6c03",
   "metadata": {},
   "source": [
    "フォルダの中のデータファイルを全部処理して，対応する結果のデータフレームのcsvと，プロット画像を保存する  \n",
    "全部の結果を合計して，総目的関数値と総計算時間をcsv出力，プロットするようにする  "
   ]
  },
  {
   "cell_type": "markdown",
   "id": "2ee490f6",
   "metadata": {},
   "source": []
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "base",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.12.7"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
