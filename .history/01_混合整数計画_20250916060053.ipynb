{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f57a500f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["見つかったCSVファイル: ['D15.csv', 'D14.csv', 'D5.csv', 'D7.csv', 'D6.csv', 'D12.csv', 'D10.csv', 'D11.csv', 'D8.csv', 'D9.csv']\n", "\n", "=== Processing data/D15.csv ===\n"]}, {"ename": "ValueError", "evalue": "invalid literal for int() with base 10: '1.0'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 486\u001b[0m\n\u001b[1;32m    483\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m平均計算時間: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtotal_calculation_time\u001b[38;5;241m/\u001b[39m\u001b[38;5;28mlen\u001b[39m(all_results)\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m秒\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    485\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 486\u001b[0m     main()\n", "Cell \u001b[0;32mIn[1], line 420\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m    418\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m csv_file \u001b[38;5;129;01min\u001b[39;00m csv_files:\n\u001b[1;32m    419\u001b[0m     file_path \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mjoin(data_folder, csv_file)\n\u001b[0;32m--> 420\u001b[0m     objective_value, calc_time, file_name \u001b[38;5;241m=\u001b[39m process_single_file(file_path)\n\u001b[1;32m    422\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m objective_value \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    423\u001b[0m         all_results\u001b[38;5;241m.\u001b[39mappend({\n\u001b[1;32m    424\u001b[0m             \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mファイル名\u001b[39m\u001b[38;5;124m'\u001b[39m: file_name,\n\u001b[1;32m    425\u001b[0m             \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m目的関数値\u001b[39m\u001b[38;5;124m'\u001b[39m: objective_value,\n\u001b[1;32m    426\u001b[0m             \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m計算時間\u001b[39m\u001b[38;5;124m'\u001b[39m: calc_time\n\u001b[1;32m    427\u001b[0m         })\n", "Cell \u001b[0;32mIn[1], line 355\u001b[0m, in \u001b[0;36mprocess_single_file\u001b[0;34m(file_path)\u001b[0m\n\u001b[1;32m    352\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m=== Processing \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m ===\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    354\u001b[0m \u001b[38;5;66;03m# CSVファイルを読み込み\u001b[39;00m\n\u001b[0;32m--> 355\u001b[0m 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト \u001b[38;5;241m=\u001b[39m read_csv(file_path)\n\u001b[1;32m    356\u001b[0m 品番数 \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(品番リスト)\n\u001b[1;32m    357\u001b[0m 期間 \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m20\u001b[39m\n", "Cell \u001b[0;32mIn[1], line 47\u001b[0m, in \u001b[0;36mread_csv\u001b[0;34m(file_path)\u001b[0m\n\u001b[1;32m     45\u001b[0m 品番リスト\u001b[38;5;241m.\u001b[39mappend(row[header\u001b[38;5;241m.\u001b[39mindex(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m素材品番\u001b[39m\u001b[38;5;124m\"\u001b[39m)])\n\u001b[1;32m     46\u001b[0m 出荷数リスト\u001b[38;5;241m.\u001b[39mappend(\u001b[38;5;28mint\u001b[39m(row[header\u001b[38;5;241m.\u001b[39mindex(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m個数\u001b[39m\u001b[38;5;124m\"\u001b[39m)]))\n\u001b[0;32m---> 47\u001b[0m 込め数リスト\u001b[38;5;241m.\u001b[39mappend(\u001b[38;5;28mint\u001b[39m(row[header\u001b[38;5;241m.\u001b[39mindex(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m込数\u001b[39m\u001b[38;5;124m\"\u001b[39m)]))\n\u001b[1;32m     49\u001b[0m cycle_time_per_unit \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mfloat\u001b[39m(row[header\u001b[38;5;241m.\u001b[39mindex(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mサイクルタイム\u001b[39m\u001b[38;5;124m\"\u001b[39m)]) \u001b[38;5;241m/\u001b[39m \u001b[38;5;241m60\u001b[39m\n\u001b[1;32m     50\u001b[0m サイクルタイムリスト\u001b[38;5;241m.\u001b[39mappend(cycle_time_per_unit)\n", "\u001b[0;31mValueError\u001b[0m: invalid literal for int() with base 10: '1.0'"]}], "source": ["import pandas as pd\n", "import pulp\n", "import csv\n", "import random\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7\n", "段替えコスト単価 = 130\n", "出荷遅れコスト単価 = 500\n", "\n", "定時 = 8 * 60 * 2\n", "最大残業時間 = 2 * 60 * 2\n", "段替え時間 = 30\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "    with open(file_path, 'r', encoding='shift-jis') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        期間数 = 20  # 期間数を定義\n", "        \n", "        rows = list(reader)\n", "        filtered_count = 0\n", "        \n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            \n", "            # 個数をチェック（200未満の場合はスキップ）\n", "            total_quantity = int(row[header.index(\"個数\")])\n", "            if total_quantity < 200:\n", "                filtered_count += 1\n", "                continue\n", "            \n", "            品番リスト.append(row[header.index(\"素材品番\")])\n", "            # 個数を期間数で割って1日あたりの出荷数にする\n", "            daily_shipment = total_quantity / 期間数\n", "            出荷数リスト.append(daily_shipment)\n", "            込め数リスト.append(int(float(row[header.index(\"込数\")])))\n", "            \n", "            cycle_time_per_unit = float(row[header.index(\"サイクルタイム\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "        \n", "        print(f\"フィルタリングされた行数（個数<200）: {filtered_count}\")\n", "        print(f\"処理対象の品番数: {len(品番リスト)}\")\n", "    \n", "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n", "        初期在庫量リスト = []\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))\n", "            初期在庫量リスト.append(random_inventory)\n", "            \n", "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def solve_mip(initial_inventory_list_arg):\n", "    \"\"\"PuLPを用いてMIPを解く関数\"\"\"\n", "    \n", "    # モデルの定義\n", "    model = pulp.LpProblem(\"ProductionScheduling\", pulp.LpMinimize)\n", "    \n", "    # インデックスの定義\n", "    品目 = range(品番数)\n", "    期間_index = range(期間)\n", "\n", "    # 決定変数\n", "    Production = pulp.LpVariable.dicts(\"Production\", (品目, 期間_index), lowBound=0, cat='Integer')\n", "    IsProduced = pulp.LpVariable.dicts(\"IsProduced\", (品目, 期間_index), cat='Binary')\n", "    Inventory = pulp.LpVariable.dicts(\"Inventory\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    Shortage = pulp.LpVariable.dicts(\"Shortage\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    WorkTime = pulp.LpVariable.dicts(\"WorkTime\", 期間_index, lowBound=0, cat='Continuous')\n", "    Overtime = pulp.LpVariable.dicts(\"Overtime\", 期間_index, lowBound=0, cat='Continuous')\n", "\n", "    # 目的関数\n", "    total_cost = pulp.lpSum(\n", "        在庫コスト単価 * Inventory[i][t] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        残業コスト単価 * Overtime[t] for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index\n", "    )\n", "    \n", "    model += total_cost, \"Total Cost\"\n", "\n", "    # 制約条件\n", "    bigM = 1000000\n", "\n", "    for i in 品目:\n", "        for t in 期間_index:\n", "            if t == 0:\n", "                # 初期在庫リストを使用\n", "                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]\n", "            else:\n", "                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]\n", "            \n", "            model += Production[i][t] <= bigM * IsProduced[i][t]\n", "\n", "    for t in 期間_index:\n", "        model += WorkTime[t] == pulp.lpSum(\n", "            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]\n", "            for i in 品目\n", "        )\n", "        \n", "        model += WorkTime[t] <= 定時 + Overtime[t]\n", "        model += WorkTime[t] <= 定時 + 最大残業時間\n", "        model += Overtime[t] >= WorkTime[t] - 定時\n", "        model += Overtime[t] >= 0\n", "\n", "    # Solverの設定\n", "    solver = pulp.GUROBI(msg=True)\n", "    \n", "    # 最適化の実行\n", "    model.solve(solver)\n", "    \n", "    print(\"ステータス:\", pulp.LpStatus[model.status])\n", "    if pulp.LpStatus[model.status] == 'Optimal':\n", "        print(\"総コスト:\", pulp.value(model.objective))\n", "\n", "        production_schedule = [[0] * 期間 for _ in range(品番数)]\n", "        for i in 品目:\n", "            for t in 期間_index:\n", "                production_schedule[i][t] = pulp.value(Production[i][t])\n", "\n", "        return production_schedule, pulp.value(model.objective)\n", "    \n", "    return None, None\n", "\n", "\n", "def simulate_production_schedule(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（MIP用の簡易版）\"\"\"\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        # 各品番の需要を処理\n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            # 在庫が不足する場合は生産\n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                # 生産量を決定（不足分を補う）\n", "                production = shortage\n", "                \n", "                # 生産時間を計算\n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30  # 段替え時間\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            # 在庫履歴に記録\n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n", "        if daily_production_time > max_daily_work_time:\n", "            # 簡易的な調整：超過分を比例配分で削減\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    # 生産量を削減し、在庫を調整\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    \n", "    return inventory_history\n", "\n", "def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3):\n", "    \"\"\"初期在庫を更新する関数（optimize_initial_inventoryと一致させた版）\"\"\"\n", "    \n", "    品番数 = len(初期在庫量リスト)\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    # h / (h+c) - optimize_initial_inventoryと同じ計算方法\n", "    prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "    \n", "    print(f\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration + 1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c) - optimize_initial_inventoryと同じロジック\n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック - optimize_initial_inventoryと同じ条件\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "def plot_results(best_individual, initial_inventory, save_path=None):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "    \n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "        \n", "        for i in range(品番数):\n", "            production = best_individual[i][t]\n", "            \n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "            \n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    if total_inventory_per_period:\n", "        axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n", "\n", "    # 2. 各期間の総生産時間＋制限ライン\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    if total_production_time_per_period:\n", "        axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n", "    axes[0, 1].legend()\n", "\n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    if total_setup_times_per_period:\n", "        axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    if total_shipment_delay_per_period:\n", "        axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n", "\n", "    plt.tight_layout()\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "    \n", "import os\n", "import time\n", "    \n", "def process_single_file(file_path):\n", "    \"\"\"単一のCSVファイルを処理する関数\"\"\"\n", "    global 品番数, 期間, 初期在庫量リスト\n", "    \n", "    print(f\"\\n=== Processing {file_path} ===\")\n", "    \n", "    # CSVファイルを読み込み\n", "    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    在庫コスト単価 = 180\n", "    出荷遅れコスト単価 = 500\n", "    \n", "    # 初期在庫量を調整\n", "    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3)\n", "    初期在庫量リスト = adjusted_initial_inventory\n", "    \n", "    print(\"=== 混合整数計画法 スケジューリング ===\")\n", "    \n", "    # 計算時間を測定\n", "    start_time = time.time()\n", "    best_solution, best_cost = solve_mip(初期在庫量リスト)\n", "    calculation_time = time.time() - start_time\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "        print(f\"計算時間: {calculation_time:.2f}秒\")\n", "        \n", "        # 結果をDataFrameに変換\n", "        result_df = pd.DataFrame(best_solution, \n", "                                index=[f\"品番_{i+1}\" for i in range(品番数)],\n", "                                columns=[f\"期間_{t+1}\" for t in range(期間)])\n", "        \n", "        # ファイル名から拡張子を除去\n", "        base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "        \n", "        # 結果をCSVとして保存\n", "        result_csv_path = f\"result//MIP_results_{base_name}.csv\"\n", "        result_df.to_csv(result_csv_path, encoding='shift-jis')\n", "        print(f\"結果をCSVファイルに保存: {result_csv_path}\")\n", "        \n", "        # プロットを作成して保存\n", "        plot_path = f\"result//MIP_results_{base_name}.png\"\n", "        plot_results(best_solution, 初期在庫量リスト, save_path=plot_path)\n", "        \n", "        return best_cost, calculation_time, base_name\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "        return None, None, None\n", "\n", "def main():\n", "    \"\"\"メイン実行関数 - 全CSVファイルを処理\"\"\"\n", "    data_folder = \"data\"\n", "    \n", "    # dataフォルダ内のすべてのCSVファイルを取得\n", "    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]\n", "    \n", "    if not csv_files:\n", "        print(\"dataフォルダにCSVファイルが見つかりません\")\n", "        return\n", "    \n", "    print(f\"見つかったCSVファイル: {csv_files}\")\n", "    \n", "    # 結果を格納するリスト\n", "    all_results = []\n", "    total_objective_value = 0\n", "    total_calculation_time = 0\n", "    \n", "    # 各CSVファイルを処理\n", "    for csv_file in csv_files:\n", "        file_path = os.path.join(data_folder, csv_file)\n", "        objective_value, calc_time, file_name = process_single_file(file_path)\n", "        \n", "        if objective_value is not None:\n", "            all_results.append({\n", "                'ファイル名': file_name,\n", "                '目的関数値': objective_value,\n", "                '計算時間': calc_time\n", "            })\n", "            total_objective_value += objective_value\n", "            total_calculation_time += calc_time\n", "    \n", "    # 集計結果をDataFrameに変換\n", "    summary_df = pd.DataFrame(all_results)\n", "    \n", "    # 合計行を追加\n", "    summary_row = pd.DataFrame({\n", "        'ファイル名': ['合計'],\n", "        '目的関数値': [total_objective_value],\n", "        '計算時間': [total_calculation_time]\n", "    })\n", "    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)\n", "    \n", "    # 集計結果をCSVとして保存\n", "    summary_csv_path = \"result//MIP_aggregate_results.csv\"\n", "    summary_df.to_csv(summary_csv_path, encoding='shift-jis', index=False)\n", "    print(f\"\\n集計結果をCSVファイルに保存: {summary_csv_path}\")\n", "    \n", "    # 集計結果のプロットを作成\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 目的関数値のプロット（合計を除く）\n", "    individual_results = summary_df[summary_df['ファイル名'] != '合計']\n", "    ax1.bar(individual_results['ファイル名'], individual_results['目的関数値'], \n", "            color='skyblue', alpha=0.7)\n", "    ax1.set_title('各データセットの目的関数値')\n", "    ax1.set_xlabel('データセット')\n", "    ax1.set_ylabel('目的関数値')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 計算時間のプロット（合計を除く）\n", "    ax2.bar(individual_results['ファイル名'], individual_results['計算時間'], \n", "            color='lightgreen', alpha=0.7)\n", "    ax2.set_title('各データセットの計算時間')\n", "    ax2.set_xlabel('データセット')\n", "    ax2.set_ylabel('計算時間 (秒)')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 集計プロットを保存\n", "    #aggregate_plot_path = \"aggregate_results.png\"\n", "    #plt.savefig(aggregate_plot_path, dpi=300, bbox_inches='tight')\n", "    #plt.show()\n", "    #print(f\"集計プロットを画像ファイルに保存: {aggregate_plot_path}\")\n", "    \n", "    # 結果の要約を表示\n", "    print(f\"\\n=== 全体の集計結果 ===\")\n", "    print(f\"処理したファイル数: {len(all_results)}\")\n", "    print(f\"総目的関数値: {total_objective_value:.2f}\")\n", "    print(f\"総計算時間: {total_calculation_time:.2f}秒\")\n", "    print(f\"平均目的関数値: {total_objective_value/len(all_results):.2f}\")\n", "    print(f\"平均計算時間: {total_calculation_time/len(all_results):.2f}秒\")\n", "    \n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "c943cbb4", "metadata": {}, "source": ["厳密解を得られない時にどうやって終わるか  \n", "500秒で終わり？  \n", "何%の解なら得られているのか？  "]}, {"cell_type": "markdown", "id": "2ee490f6", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}