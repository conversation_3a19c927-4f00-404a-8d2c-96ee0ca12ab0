{"cells": [{"cell_type": "code", "execution_count": 8, "id": "6e757c78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [1200, 1000, 500, 50]\n", "  更新後の初期在庫量: [3432, 2383, 1290, 188]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [1200, 1000, 500, 50]\n", "  更新後の初期在庫量: [2232, 1383, 790, 138]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [1200, 1000, 500, 50]\n", "  更新後の初期在庫量: [1032, 383, 290, 88]\n", "--- 調整イテレーション 4 ---\n", "  今回の調整量: [1032, 383, 290, 50]\n", "  更新後の初期在庫量: [0, 0, 0, 38]\n", "--- 調整イテレーション 5 ---\n", "  今回の調整量: [0, 0, 0, 38]\n", "  更新後の初期在庫量: [0, 0, 0, 0]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタートローカルサーチ スケジューリング ===\n", "--- Start 1/30 ---\n", "  New best solution found with total cost: 282880.00\n", "--- Start 2/30 ---\n", "--- Start 3/30 ---\n", "--- Start 4/30 ---\n", "--- Start 5/30 ---\n", "--- Start 6/30 ---\n", "--- Start 7/30 ---\n", "--- Start 8/30 ---\n", "--- Start 9/30 ---\n", "--- Start 10/30 ---\n", "--- Start 11/30 ---\n", "--- Start 12/30 ---\n", "--- Start 13/30 ---\n", "--- Start 14/30 ---\n", "--- Start 15/30 ---\n", "--- Start 16/30 ---\n", "--- Start 17/30 ---\n", "--- Start 18/30 ---\n", "--- Start 19/30 ---\n", "--- Start 20/30 ---\n", "--- Start 21/30 ---\n", "--- Start 22/30 ---\n", "--- Start 23/30 ---\n", "--- Start 24/30 ---\n", "--- Start 25/30 ---\n", "--- Start 26/30 ---\n", "--- Start 27/30 ---\n", "--- Start 28/30 ---\n", "  New best solution found with total cost: 279070.00\n", "--- Start 29/30 ---\n", "--- Start 30/30 ---\n", "  New best solution found with total cost: 252130.00\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 252130.00\n", "\n", "=== 結果のプロット ===\n"]}, {"data": {"image/png": "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***********************************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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["時間制約違反: 0 期間\n"]}], "source": ["import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7\n", "段替えコスト単価 = 130\n", "出荷遅れコスト単価 = 500\n", "\n", "定時 = 8 * 60 * 2\n", "最大残業時間 = 2 * 60 * 2\n", "段替え時間 = 30\n", "品切れ率の許容値 = 0.05\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            品番リスト.append(row[header.index(\"part_number\")])\n", "            出荷数リスト.append(int(row[header.index(\"shipment\")]))\n", "            収容数リスト.append(int(row[header.index(\"capacity\")]))\n", "            \n", "            cycle_time_per_unit = float(row[header.index(\"cycle_time\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            込め数リスト.append(int(row[header.index(\"cabity\")]))\n", "\n", "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n", "        初期在庫量リスト = []\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(shipment * 3, shipment * 5)\n", "            初期在庫量リスト.append(random_inventory)\n", "\n", "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(solution, current_initial_inventory):\n", "    \"\"\"総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数\"\"\"\n", "    \n", "    # 最小化コスト\n", "    total_inventory_cost = 0\n", "    total_overtime_cost = 0\n", "    total_setup_cost = 0\n", "    total_shipment_delay_cost = 0\n", "    \n", "    inventory = current_initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            production = solution[t][i]\n", "            \n", "            # 生産がある場合に段替えをする\n", "            if production > 0:\n", "                daily_setup_count += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "            \n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "            inventory_history[i].append(inventory[i])\n", "            \n", "            # 出荷遅れコスト\n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount\n", "            \n", "            # 在庫コスト\n", "            if inventory[i] > 0:\n", "                total_inventory_cost += 在庫コスト単価 * inventory[i]\n", "        \n", "        # 段替えコスト\n", "        total_setup_cost += 段替えコスト単価 * daily_setup_count\n", "        \n", "        # 残業コスト\n", "        if daily_time > 定時:\n", "            overtime = daily_time - 定時\n", "            total_overtime_cost += 残業コスト単価 * overtime\n", "        \n", "        # 最大稼働時間超過ペナルティ（違反する場合はコストに加算）\n", "        if daily_time > 定時 + 最大残業時間:\n", "            work_time_penalty = (daily_time - (定時 + 最大残業時間)) * (残業コスト単価 * 1000000)\n", "            total_overtime_cost += work_time_penalty\n", "            \n", "    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost\n", "    \n", "    return total_cost, inventory_history\n", "\n", "def generate_initial_solution(current_initial_inventory):\n", "    \"\"\"初期解を生成する関数\"\"\"\n", "    solution = []\n", "    temp_inventory = current_initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_productions = [0] * 品番数\n", "        daily_time = 0\n", "        \n", "        # 在庫がない品番を優先的に生産する\n", "        priorities = []\n", "        for i in range(品番数):\n", "            shortage_estimate = max(0, 出荷数リスト[i] - temp_inventory[i])\n", "            priorities.append((shortage_estimate, i))\n", "        \n", "        priorities.sort(key=lambda x: x[0], reverse=True)\n", "        \n", "        for shortage_estimate, i in priorities:\n", "            if shortage_estimate > 0:\n", "                setup_time = 30\n", "                remaining_time = max_daily_work_time - daily_time\n", "                \n", "                if remaining_time > setup_time:\n", "                    cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]\n", "                    if cycle_time_per_unit > 0:\n", "                        max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)\n", "                        \n", "                        if max_producible_by_time > 0:\n", "                            target_production = shortage_estimate + random.randint(0, 50)\n", "                            production = min(target_production, max_producible_by_time)\n", "                            \n", "                            daily_productions[i] = production\n", "                            daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "        \n", "        solution.append(daily_productions)\n", "        \n", "        for i in range(品番数):\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            \n", "    return solution\n", "\n", "def get_neighbors(current_solution):\n", "    \"\"\"近傍解を生成する関数\"\"\"\n", "    neighbors = []\n", "    \n", "    # 2つの生産量を入れ替える\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "        i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "        \n", "        if (t1, i1) != (t2, i2):\n", "            neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "            neighbors.append(neighbor)\n", "            \n", "    # 特定の生産量を増減させる\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t = random.randint(0, 期間 - 1)\n", "        i = random.randint(0, 品番数 - 1)\n", "        \n", "        change = random.randint(-50, 50)\n", "        neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "        neighbors.append(neighbor)\n", "        \n", "    return neighbors\n", "\n", "def local_search(initial_solution, current_initial_inventory):\n", "    \"\"\"ローカルサーチを実行する関数\"\"\"\n", "    current_solution = initial_solution\n", "    current_cost, _ = evaluate(current_solution, current_initial_inventory)\n", "    \n", "    while True:\n", "        neighbors = get_neighbors(current_solution)\n", "        best_neighbor = None\n", "        best_neighbor_cost = float('inf')\n", "        \n", "        for neighbor in neighbors:\n", "            neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)\n", "            if neighbor_cost < best_neighbor_cost:\n", "                best_neighbor = neighbor\n", "                best_neighbor_cost = neighbor_cost\n", "        \n", "        if best_neighbor_cost < current_cost:\n", "            current_solution = best_neighbor\n", "            current_cost = best_neighbor_cost\n", "        else:\n", "            break\n", "            \n", "    return current_solution, current_cost\n", "\n", "def multi_start_local_search(num_starts, current_initial_inventory):\n", "    \"\"\"多スタートローカルサーチを実行する関数\"\"\"\n", "    best_solution_overall = None\n", "    best_cost_overall = float('inf')\n", "    \n", "    for i in range(num_starts):\n", "        print(f\"--- Start {i+1}/{num_starts} ---\")\n", "        \n", "        initial_solution = generate_initial_solution(current_initial_inventory)\n", "        \n", "        local_optimal_solution, local_optimal_cost = local_search(initial_solution, current_initial_inventory)\n", "        \n", "        if local_optimal_cost < best_cost_overall:\n", "            best_cost_overall = local_optimal_cost\n", "            best_solution_overall = local_optimal_solution\n", "            print(f\"  New best solution found with total cost: {best_cost_overall:.2f}\")\n", "            \n", "    return best_solution_overall, best_cost_overall\n", "\n", "def simulate_production_schedule_simple(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（簡易版）\"\"\"\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        # 各品番の需要を処理\n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            # 在庫が不足する場合は生産\n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                # 生産量を決定（不足分を補う）\n", "                production = shortage\n", "                \n", "                # 生産時間を計算\n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30  # 段替え時間\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            # 在庫履歴に記録\n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n", "        if daily_production_time > max_daily_work_time:\n", "            # 簡易的な調整：超過分を比例配分で削減\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    # 生産量を削減し、在庫を調整\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    \n", "    return inventory_history\n", "\n", "def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):\n", "    \"\"\"初期在庫量を最適化する関数（簡易シミュレーション使用版）\"\"\"\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    print(\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration+1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule_simple(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c)\n", "            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "            \n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "\n", "def plot_results(best_individual, initial_inventory, save_path=None):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            production = best_individual[t][i]\n", "\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    if total_inventory_per_period:\n", "        axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n", "\n", "    # 2. 各期間の総生産時間＋制限ライン\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n", "    axes[0, 1].legend()\n", "    \n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n", "\n", "    plt.tight_layout()\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "\n", "    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period\n", "\n", "\n", "def main():\n", "    \"\"\"メイン実行関数\"\"\"\n", "    global 品番数, 期間, 初期在庫量リスト\n", "\n", "    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv('data//D36.csv')\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    在庫コスト単価 = 180\n", "    出荷遅れコスト単価 = 500\n", "    \n", "    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5)\n", "    初期在庫量リスト = adjusted_initial_inventory\n", "\n", "    print(\"=== 多スタートローカルサーチ スケジューリング ===\")\n", "    \n", "    num_starts = 30\n", "    best_solution, best_cost = multi_start_local_search(num_starts, 初期在庫量リスト)\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "\n", "        plot_results(best_solution, 初期在庫量リスト)\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "24165dfe", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}