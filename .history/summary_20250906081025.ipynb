{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 最適化手法比較サマリー\n", "\n", "このノートブックでは、混合整数計画法（MIP）と多スタートローカルサーチの結果を比較します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import numpy as np\n", "import os\n", "\n", "# 結果ファイルのパス\n", "mip_results_path = 'result/MIP_aggregate_results.csv'\n", "multistart_results_path = 'result/multi_start_aggregate_results.csv'\n", "\n", "print(\"=== 最適化手法比較サマリー ===\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# CSVファイルを読み込み\n", "try:\n", "    mip_df = pd.read_csv(mip_results_path, encoding='utf-8')\n", "    print(\"MIP結果ファイルを読み込みました\")\n", "    print(mip_df)\n", "except Exception as e:\n", "    print(f\"MIP結果ファイルの読み込みエラー: {e}\")\n", "    # エンコーディングを変更して再試行\n", "    try:\n", "        mip_df = pd.read_csv(mip_results_path, encoding='shift_jis')\n", "        print(\"MIP結果ファイルを読み込みました（Shift_JIS）\")\n", "        print(mip_df)\n", "    except Exception as e2:\n", "        print(f\"MIP結果ファイルの読み込みに失敗: {e2}\")\n", "        mip_df = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["try:\n", "    multistart_df = pd.read_csv(multistart_results_path, encoding='utf-8')\n", "    print(\"\\n多スタートローカルサーチ結果ファイルを読み込みました\")\n", "    print(multistart_df)\n", "except Exception as e:\n", "    print(f\"多スタートローカルサーチ結果ファイルの読み込みエラー: {e}\")\n", "    multistart_df = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# データの前処理と比較\n", "if mip_df is not None and multistart_df is not None:\n", "    # 合計行を抽出\n", "    mip_total = mip_df[mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    multistart_total = multistart_df[multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    \n", "    if len(mip_total) > 0 and len(multistart_total) > 0:\n", "        # 合計値を取得\n", "        mip_objective = float(mip_total.iloc[0, 1])  # 目的関数値\n", "        mip_time = float(mip_total.iloc[0, 2])       # 計算時間\n", "        \n", "        multistart_objective = float(multistart_total.iloc[0, 1])  # 目的関数値\n", "        multistart_time = float(multistart_total.iloc[0, 2])       # 計算時間\n", "        \n", "        print(f\"\\n=== 合計値比較 ===\")\n", "        print(f\"MIP - 目的関数値: {mip_objective:.2f}, 計算時間: {mip_time:.4f}秒\")\n", "        print(f\"多スタートローカルサーチ - 目的関数値: {multistart_objective:.2f}, 計算時間: {multistart_time:.4f}秒\")\n", "        \n", "        # 改善率の計算\n", "        objective_improvement = ((multistart_objective - mip_objective) / mip_objective) * 100\n", "        time_ratio = multistart_time / mip_time\n", "        \n", "        print(f\"\\n=== 比較結果 ===\")\n", "        print(f\"目的関数値の差: {multistart_objective - mip_objective:.2f}\")\n", "        print(f\"目的関数値の変化率: {objective_improvement:.2f}% (正の値は多スタートが悪い)\")\n", "        print(f\"計算時間の比率: {time_ratio:.2f} (多スタート/MIP)\")\n", "    else:\n", "        print(\"合計行が見つかりませんでした\")\n", "else:\n", "    print(\"データの読み込みに失敗したため、比較できません\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}