{"cells": [{"cell_type": "code", "execution_count": 9, "id": "6e757c78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["見つかったCSVファイル: ['D28.csv', 'D36.csv', 'D42.csv']\n", "\n", "=== Processing data/D28.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [550, 300, 500, 50, 1000, 10, 600, 100]\n", "  更新後の初期在庫量: [1122, 702, 1698, 199, 3047, 38, 2289, 398]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [550, 300, 500, 50, 1000, 10, 600, 100]\n", "  更新後の初期在庫量: [572, 402, 1198, 149, 2047, 28, 1689, 298]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [550, 300, 500, 50, 1000, 10, 600, 100]\n", "  更新後の初期在庫量: [22, 102, 698, 99, 1047, 18, 1089, 198]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタートローカルサーチ スケジューリング ===\n", "--- Start 1/30 ---\n", "  New best solution found with total cost: 847730.76\n", "--- Start 2/30 ---\n", "--- Start 3/30 ---\n", "--- Start 4/30 ---\n", "--- Start 5/30 ---\n", "--- Start 6/30 ---\n", "--- Start 7/30 ---\n", "--- Start 8/30 ---\n", "  New best solution found with total cost: 802127.71\n", "--- Start 9/30 ---\n", "--- Start 10/30 ---\n", "--- Start 11/30 ---\n", "--- Start 12/30 ---\n", "--- Start 13/30 ---\n", "--- Start 14/30 ---\n", "--- Start 15/30 ---\n", "--- Start 16/30 ---\n", "--- Start 17/30 ---\n", "--- Start 18/30 ---\n", "--- Start 19/30 ---\n", "--- Start 20/30 ---\n", "--- Start 21/30 ---\n", "--- Start 22/30 ---\n", "  New best solution found with total cost: 640677.33\n", "--- Start 23/30 ---\n", "--- Start 24/30 ---\n", "--- Start 25/30 ---\n", "--- Start 26/30 ---\n", "--- Start 27/30 ---\n", "--- Start 28/30 ---\n", "--- Start 29/30 ---\n", "--- Start 30/30 ---\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 640677.33\n", "計算時間: 0.03秒\n", "結果をCSVファイルに保存: result/multi_start_results_D28.csv\n", "\n", "=== 結果のプロット ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/_p/gpx29nx95pn__7720hyny3sm0000gn/T/ipykernel_49793/2975421777.py:496: UserWarning: Attempting to set identical low and high ylims makes transformation singular; automatically expanding.\n", "  axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["プロットを保存: result/multi_start_results_D28.png\n", "時間制約違反: 0 期間\n", "\n", "=== Processing data/D36.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [1400, 1000, 500, 200]\n", "  更新後の初期在庫量: [4220, 3732, 1394, 746]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [1400, 1000, 500, 200]\n", "  更新後の初期在庫量: [2820, 2732, 894, 546]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [1400, 1000, 500, 200]\n", "  更新後の初期在庫量: [1420, 1732, 394, 346]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタートローカルサーチ スケジューリング ===\n", "--- Start 1/30 ---\n", "  New best solution found with total cost: 625523.91\n", "--- Start 2/30 ---\n", "--- Start 3/30 ---\n", "  New best solution found with total cost: 353521.92\n", "--- Start 4/30 ---\n", "--- Start 5/30 ---\n", "--- Start 6/30 ---\n", "--- Start 7/30 ---\n", "--- Start 8/30 ---\n", "--- Start 9/30 ---\n", "--- Start 10/30 ---\n", "--- Start 11/30 ---\n", "--- Start 12/30 ---\n", "--- Start 13/30 ---\n", "--- Start 14/30 ---\n", "--- Start 15/30 ---\n", "--- Start 16/30 ---\n", "--- Start 17/30 ---\n", "--- Start 18/30 ---\n", "--- Start 19/30 ---\n", "--- Start 20/30 ---\n", "--- Start 21/30 ---\n", "--- Start 22/30 ---\n", "--- Start 23/30 ---\n", "--- Start 24/30 ---\n", "--- Start 25/30 ---\n", "--- Start 26/30 ---\n", "--- Start 27/30 ---\n", "--- Start 28/30 ---\n", "--- Start 29/30 ---\n", "--- Start 30/30 ---\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 353521.92\n", "計算時間: 0.02秒\n", "結果をCSVファイルに保存: result/multi_start_results_D36.csv\n", "\n", "=== 結果のプロット ===\n", "プロットを保存: result/multi_start_results_D36.png\n", "時間制約違反: 0 期間\n", "\n", "=== Processing data/D42.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [600, 400, 800, 50, 1200, 100]\n", "  更新後の初期在庫量: [1260, 954, 1607, 157, 4172, 265]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [600, 400, 800, 50, 1200, 100]\n", "  更新後の初期在庫量: [660, 554, 807, 107, 2972, 165]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [600, 400, 800, 50, 1200, 100]\n", "  更新後の初期在庫量: [60, 154, 7, 57, 1772, 65]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタートローカルサーチ スケジューリング ===\n", "--- Start 1/30 ---\n", "  New best solution found with total cost: 802083.96\n", "--- Start 2/30 ---\n", "  New best solution found with total cost: 580531.67\n", "--- Start 3/30 ---\n", "--- Start 4/30 ---\n", "  New best solution found with total cost: 321644.41\n", "--- Start 5/30 ---\n", "--- Start 6/30 ---\n", "--- Start 7/30 ---\n", "--- Start 8/30 ---\n", "--- Start 9/30 ---\n", "--- Start 10/30 ---\n", "--- Start 11/30 ---\n", "--- Start 12/30 ---\n", "  New best solution found with total cost: 301761.75\n", "--- Start 13/30 ---\n", "--- Start 14/30 ---\n", "--- Start 15/30 ---\n", "--- Start 16/30 ---\n", "--- Start 17/30 ---\n", "--- Start 18/30 ---\n", "--- Start 19/30 ---\n", "--- Start 20/30 ---\n", "--- Start 21/30 ---\n", "--- Start 22/30 ---\n", "--- Start 23/30 ---\n", "--- Start 24/30 ---\n", "--- Start 25/30 ---\n", "--- Start 26/30 ---\n", "--- Start 27/30 ---\n", "--- Start 28/30 ---\n", "--- Start 29/30 ---\n", "--- Start 30/30 ---\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 301761.75\n", "計算時間: 0.02秒\n", "結果をCSVファイルに保存: result/multi_start_results_D42.csv\n", "\n", "=== 結果のプロット ===\n", "プロットを保存: result/multi_start_results_D42.png\n", "時間制約違反: 0 期間\n", "\n", "集計結果をCSVファイルに保存: result/multi_start_aggregate_results.csv\n", "\n", "=== 全体の集計結果 ===\n", "処理したファイル数: 3\n", "総目的関数値: 1295961.00\n", "総計算時間: 0.06秒\n", "平均目的関数値: 431987.00\n", "平均計算時間: 0.02秒\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7\n", "段替えコスト単価 = 130\n", "出荷遅れコスト単価 = 500\n", "\n", "定時 = 8 * 60 * 2\n", "最大残業時間 = 2 * 60 * 2\n", "段替え時間 = 30\n", "品切れ率の許容値 = 0.05\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            品番リスト.append(row[header.index(\"part_number\")])\n", "            出荷数リスト.append(int(row[header.index(\"shipment\")]))\n", "            収容数リスト.append(int(row[header.index(\"capacity\")]))\n", "            \n", "            cycle_time_per_unit = float(row[header.index(\"cycle_time\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            込め数リスト.append(int(row[header.index(\"cabity\")]))\n", "\n", "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n", "        初期在庫量リスト = []\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(shipment * 3, shipment * 5)\n", "            初期在庫量リスト.append(random_inventory)\n", "\n", "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(solution, current_initial_inventory):\n", "    \"\"\"総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数\"\"\"\n", "    \n", "    # 最小化コスト\n", "    total_inventory_cost = 0\n", "    total_overtime_cost = 0\n", "    total_setup_cost = 0\n", "    total_shipment_delay_cost = 0\n", "    \n", "    inventory = current_initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            production = solution[t][i]\n", "            \n", "            # 生産がある場合に段替えをする\n", "            if production > 0:\n", "                daily_setup_count += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "            \n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "            inventory_history[i].append(inventory[i])\n", "            \n", "            # 出荷遅れコスト\n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount\n", "            \n", "            # 在庫コスト\n", "            if inventory[i] > 0:\n", "                total_inventory_cost += 在庫コスト単価 * inventory[i]\n", "        \n", "        # 段替えコスト\n", "        total_setup_cost += 段替えコスト単価 * daily_setup_count\n", "        \n", "        # 残業コスト\n", "        if daily_time > 定時:\n", "            overtime = daily_time - 定時\n", "            total_overtime_cost += 残業コスト単価 * overtime\n", "        \n", "        # 最大稼働時間超過ペナルティ（違反する場合はコストに加算）\n", "        if daily_time > 定時 + 最大残業時間:\n", "            work_time_penalty = (daily_time - (定時 + 最大残業時間)) * (残業コスト単価 * 1000000)\n", "            total_overtime_cost += work_time_penalty\n", "            \n", "    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost\n", "    \n", "    return total_cost, inventory_history\n", "\n", "def generate_initial_solution(current_initial_inventory):\n", "    \"\"\"初期解を生成する関数\"\"\"\n", "    solution = []\n", "    temp_inventory = current_initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    \n", "    # 全期間の総需要を計算\n", "    total_demand = [sum(出荷数リスト) * 期間 for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_productions = [0] * 品番数\n", "        daily_time = 0\n", "        \n", "        # 在庫が不足している品番と、生産することで在庫が大幅に増える品番を区別\n", "        priority_queue = []\n", "        for i in range(品番数):\n", "            # 在庫がマイナスになる品番は高優先度\n", "            remaining_inventory = temp_inventory[i] - 出荷数リスト[i]\n", "            priority_queue.append((remaining_inventory, i))\n", "            \n", "        priority_queue.sort() # 在庫が少ない順に並べる\n", "        \n", "        for remaining_inventory, i in priority_queue:\n", "            setup_time = 30 if daily_productions[i] == 0 else 0\n", "            remaining_time = max_daily_work_time - daily_time\n", "            \n", "            if remaining_time <= setup_time:\n", "                break\n", "            \n", "            cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]\n", "            if cycle_time_per_unit == 0: continue\n", "            \n", "            max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)\n", "            \n", "            # 在庫がマイナスになる場合、その不足分を補う\n", "            target_production = 0\n", "            if remaining_inventory < 0:\n", "                target_production = abs(remaining_inventory)\n", "            else:\n", "                # 在庫が十分な場合は、今後の需要を見越して少しだけ生産\n", "                target_production = random.randint(0, int(出荷数リスト[i] * 1.5))\n", "                \n", "            production = min(target_production, max_producible_by_time)\n", "            \n", "            # 生産量が妥当な範囲内かチェック\n", "            if production > 0:\n", "                daily_productions[i] = production\n", "                daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "        \n", "        solution.append(daily_productions)\n", "        \n", "        for i in range(品番数):\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            \n", "    return solution\n", "\n", "\"\"\"def get_neighbors(current_solution):\n", "    neighbors = []\n", "    \n", "    # 2つの生産量を入れ替える\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "        i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "        \n", "        if (t1, i1) != (t2, i2):\n", "            neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "            neighbors.append(neighbor)\n", "            \n", "    # 特定の生産量を増減させる\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t = random.randint(0, 期間 - 1)\n", "        i = random.randint(0, 品番数 - 1)\n", "        \n", "        change = random.randint(-50, 50)\n", "        neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "        neighbors.append(neighbor)\n", "        \n", "    return neighbors\n", "\"\"\"\n", "\n", "def get_neighbors(current_solution):\n", "    \"\"\"より多様な近傍解を生成する関数\"\"\"\n", "    neighbors = []\n", "    num_neighbors = 10 # 生成する近傍解の数を増やす\n", "    \n", "    for _ in range(num_neighbors):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        \n", "        # 近傍探索のタイプをランダムに選択\n", "        operation_type = random.choice(['swap', 'shift_time', 'adjust_amount', 'consolidate'])\n", "        \n", "        if operation_type == 'swap':\n", "            # 2つの生産量を入れ替える (従来のロジック)\n", "            t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "            i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "            \n", "            if (t1, i1) != (t2, i2):\n", "                neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "\n", "        elif operation_type == 'shift_time':\n", "            # ある期間の生産量を別の期間にシフトする\n", "            t_from = random.randint(0, 期間 - 1)\n", "            t_to = random.randint(0, 期間 - 1)\n", "            i = random.randint(0, 品番数 - 1)\n", "            \n", "            if t_from != t_to:\n", "                production_amount = neighbor[t_from][i]\n", "                if production_amount > 0:\n", "                    neighbor[t_from][i] = 0\n", "                    neighbor[t_to][i] += production_amount\n", "\n", "        elif operation_type == 'adjust_amount':\n", "            # 特定の生産量を増減させる (従来のロジックに似ているが、調整幅を広げる)\n", "            t = random.randint(0, 期間 - 1)\n", "            i = random.randint(0, 品番数 - 1)\n", "            \n", "            change = random.randint(-100, 100)\n", "            neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "\n", "        elif operation_type == 'consolidate':\n", "            # 同じ品番の生産を特定の期間に集約する\n", "            i = random.randint(0, 品番数 - 1)\n", "            t_target = random.randint(0, 期間 - 1)\n", "            \n", "            total_production_for_part = 0\n", "            for t in range(期間):\n", "                if t != t_target:\n", "                    total_production_for_part += neighbor[t][i]\n", "                    neighbor[t][i] = 0\n", "            \n", "            neighbor[t_target][i] += total_production_for_part\n", "            \n", "        neighbors.append(neighbor)\n", "        \n", "    return neighbors\n", "\n", "def local_search(initial_solution, current_initial_inventory):\n", "    \"\"\"ローカルサーチを実行する関数\"\"\"\n", "    current_solution = initial_solution\n", "    current_cost, _ = evaluate(current_solution, current_initial_inventory)\n", "    \n", "    while True:\n", "        neighbors = get_neighbors(current_solution)\n", "        best_neighbor = None\n", "        best_neighbor_cost = float('inf')\n", "        \n", "        for neighbor in neighbors:\n", "            neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)\n", "            if neighbor_cost < best_neighbor_cost:\n", "                best_neighbor = neighbor\n", "                best_neighbor_cost = neighbor_cost\n", "        \n", "        if best_neighbor_cost < current_cost:\n", "            current_solution = best_neighbor\n", "            current_cost = best_neighbor_cost\n", "        else:\n", "            break\n", "            \n", "    return current_solution, current_cost\n", "\n", "def multi_start_local_search(num_starts, current_initial_inventory):\n", "    \"\"\"多スタートローカルサーチを実行する関数\"\"\"\n", "    best_solution_overall = None\n", "    best_cost_overall = float('inf')\n", "    \n", "    for i in range(num_starts):\n", "        print(f\"--- Start {i+1}/{num_starts} ---\")\n", "        \n", "        initial_solution = generate_initial_solution(current_initial_inventory)\n", "        \n", "        local_optimal_solution, local_optimal_cost = local_search(initial_solution, current_initial_inventory)\n", "        \n", "        if local_optimal_cost < best_cost_overall:\n", "            best_cost_overall = local_optimal_cost\n", "            best_solution_overall = local_optimal_solution\n", "            print(f\"  New best solution found with total cost: {best_cost_overall:.2f}\")\n", "            \n", "    return best_solution_overall, best_cost_overall\n", "\n", "def simulate_production_schedule_simple(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（簡易版）\"\"\"\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        # 各品番の需要を処理\n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            # 在庫が不足する場合は生産\n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                # 生産量を決定（不足分を補う）\n", "                production = shortage\n", "                \n", "                # 生産時間を計算\n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30  # 段替え時間\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            # 在庫履歴に記録\n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n", "        if daily_production_time > max_daily_work_time:\n", "            # 簡易的な調整：超過分を比例配分で削減\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    # 生産量を削減し、在庫を調整\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    \n", "    return inventory_history\n", "\n", "def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):\n", "    \"\"\"初期在庫量を最適化する関数（簡易シミュレーション使用版）\"\"\"\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    print(\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration+1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule_simple(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c)\n", "            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "            \n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "\n", "def plot_results(best_individual, initial_inventory, save_path=None):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            production = best_individual[t][i]\n", "\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    if total_inventory_per_period:\n", "        axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n", "\n", "    # 2. 各期間の総生産時間＋制限ライン\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    if total_production_time_per_period:\n", "        axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n", "    axes[0, 1].legend()\n", "    \n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    if total_setup_times_per_period:\n", "        axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    if total_shipment_delay_per_period:\n", "        axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n", "\n", "    plt.tight_layout()\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "\n", "    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period\n", "\n", "\n", "import os\n", "import time\n", "    \n", "def process_single_file(file_path):\n", "    \"\"\"単一のCSVファイルを処理する関数\"\"\"\n", "    global 品番数, 期間, 初期在庫量リスト\n", "    \n", "    print(f\"\\n=== Processing {file_path} ===\")\n", "    \n", "    # CSVファイルを読み込み\n", "    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    在庫コスト単価 = 180\n", "    出荷遅れコスト単価 = 500\n", "    \n", "    # 初期在庫量を調整\n", "    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3)\n", "    初期在庫量リスト = adjusted_initial_inventory\n", "    \n", "    print(\"=== 多スタートローカルサーチ スケジューリング ===\")\n", "    \n", "    # 計算時間を測定\n", "    start_time = time.time()\n", "    num_starts = 30\n", "    best_solution, best_cost = multi_start_local_search(num_starts, 初期在庫量リスト)\n", "    calculation_time = time.time() - start_time\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "        print(f\"計算時間: {calculation_time:.2f}秒\")\n", "        \n", "        # 結果をDataFrameに変換（期間×品番の形式）\n", "        result_df = pd.DataFrame(best_solution, \n", "                                index=[f\"期間_{t+1}\" for t in range(期間)],\n", "                                columns=[f\"品番_{i+1}\" for i in range(品番数)])\n", "        \n", "        # ファイル名から拡張子を除去\n", "        base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "        \n", "        # 結果をCSVとして保存\n", "        result_csv_path = f\"result/multi_start_results_{base_name}.csv\"\n", "        result_df.to_csv(result_csv_path)\n", "        print(f\"結果をCSVファイルに保存: {result_csv_path}\")\n", "        \n", "        # プロットを作成して保存\n", "        plot_path = f\"result/multi_start_results_{base_name}.png\"\n", "        plot_results(best_solution, 初期在庫量リスト, save_path=plot_path)\n", "        \n", "        return best_cost, calculation_time, base_name\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "        return None, None, None\n", "\n", "def main():\n", "    \"\"\"メイン実行関数 - 全CSVファイルを処理\"\"\"\n", "    data_folder = \"data\"\n", "    \n", "    # dataフォルダ内のすべてのCSVファイルを取得\n", "    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]\n", "    \n", "    if not csv_files:\n", "        print(\"dataフォルダにCSVファイルが見つかりません\")\n", "        return\n", "    \n", "    print(f\"見つかったCSVファイル: {csv_files}\")\n", "    \n", "    # 結果を格納するリスト\n", "    all_results = []\n", "    total_objective_value = 0\n", "    total_calculation_time = 0\n", "    \n", "    # 各CSVファイルを処理\n", "    for csv_file in csv_files:\n", "        file_path = os.path.join(data_folder, csv_file)\n", "        objective_value, calc_time, file_name = process_single_file(file_path)\n", "        \n", "        if objective_value is not None:\n", "            all_results.append({\n", "                'ファイル名': file_name,\n", "                '目的関数値': objective_value,\n", "                '計算時間': calc_time\n", "            })\n", "            total_objective_value += objective_value\n", "            total_calculation_time += calc_time\n", "    \n", "    # 集計結果をDataFrameに変換\n", "    summary_df = pd.DataFrame(all_results)\n", "    \n", "    # 合計行を追加\n", "    summary_row = pd.DataFrame({\n", "        'ファイル名': ['合計'],\n", "        '目的関数値': [total_objective_value],\n", "        '計算時間': [total_calculation_time]\n", "    })\n", "    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)\n", "    \n", "    # 集計結果をCSVとして保存\n", "    summary_csv_path = \"result/multi_start_aggregate_results.csv\"\n", "    summary_df.to_csv(summary_csv_path, index=False)\n", "    print(f\"\\n集計結果をCSVファイルに保存: {summary_csv_path}\")\n", "    \n", "    # 結果の要約を表示\n", "    print(f\"\\n=== 全体の集計結果 ===\")\n", "    print(f\"処理したファイル数: {len(all_results)}\")\n", "    print(f\"総目的関数値: {total_objective_value:.2f}\")\n", "    print(f\"総計算時間: {total_calculation_time:.2f}秒\")\n", "    if len(all_results) > 0:\n", "        print(f\"平均目的関数値: {total_objective_value/len(all_results):.2f}\")\n", "        print(f\"平均計算時間: {total_calculation_time/len(all_results):.2f}秒\")\n", "        \n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "24165dfe", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}