"""
Production Scheduling Optimization Comparison Tool
Compares Mixed Integer Programming vs Multi-Start Local Search

This script runs both optimization methods and provides detailed comparison
of calculation time and objective values.
"""

import time
import csv
import random
import copy
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pulp

# Global parameters
在庫コスト単価 = 70
残業コスト単価 = 500
段替えコスト単価 = 500
出荷遅れコスト単価 = 500
定時 = 8 * 60 * 2  # 8時間 * 60分 * 2シフト = 960分
最大残業時間 = 2 * 60 * 2  # 2時間 * 60分 * 2シフト = 240分
段替え時間 = 30  # 分

# Global variables
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 20


def read_csv(file_path):
    """CSVファイルからデータを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)
            
            品番リスト = []
            出荷数リスト = []
            収容数リスト = []
            サイクルタイムリスト = []
            込め数リスト = []
            
            # CSVからデータを読み込む
            rows = list(reader)
            for row in rows:
                if len(row) == 0:
                    continue
                品番リスト.append(row[header.index("part_number")])
                出荷数リスト.append(int(row[header.index("shipment")]))
                収容数リスト.append(int(row[header.index("capacity")]))
                
                # サイクルタイムを分単位に変換
                cycle_time_per_unit = float(row[header.index("cycle_time")]) / 60
                サイクルタイムリスト.append(cycle_time_per_unit)
                
                込め数リスト.append(int(row[header.index("cabity")]))

            # 出荷数に基づいて初期在庫量（処理前）をランダムに設定
            初期在庫量リスト = []
            for shipment in 出荷数リスト:
                random_inventory = random.randint(shipment * 3, shipment * 5)
                初期在庫量リスト.append(random_inventory)

            return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    except FileNotFoundError:
        print(f"ファイル '{file_path}' が見つかりません。")
        return None, None, None, None, None, None
    except Exception as e:
        print(f"CSVファイルの読み込み中にエラーが発生しました: {e}")
        return None, None, None, None, None, None


def adjust_initial_inventory(s, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):
    """初期在庫水準を調整するアルゴリズム"""
    global 品番数, 期間

    print("\n=== 初期在庫水準の調整アルゴリズム開始 ===")

    for iteration in range(max_iterations):
        print(f"--- 調整イテレーション {iteration + 1} ---")

        # 各在庫点について在庫量の分布を求める
        inventory_distributions = [[] for _ in range(品番数)]

        for sim in range(num_simulations):
            temp_inventory = s[:]

            for t in range(期間):
                for i in range(品番数):
                    temp_inventory[i] -= 出荷数リスト[i]
                    # 在庫が負になった場合は0にする
                    temp_inventory[i] = max(0, temp_inventory[i])
                    inventory_distributions[i].append(temp_inventory[i])

        adjustments = [0] * 品番数

        # 各在庫点について在庫量の最適調整量r^*を求める
        for i in range(品番数):
            if not inventory_distributions[i]:
                continue

            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()
            cumulative_distribution = inventory_counts.cumsum()

            # sum_{x <= r-1} f(x) <= h / (h + c)
            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)

            best_r = 0
            for r in cumulative_distribution.index:
                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)
                if prob_at_r_minus_1 <= prob_target:
                    best_r = r
                else:
                    break

            adjustments[i] = s[i] - best_r

        print(f"  今回の調整量: {adjustments}")

        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する
        new_s = [max(0, s[i] - adjustments[i]) for i in range(品番数)]  # 負の値を防ぐ

        # 終了条件のチェック
        if all(abs(adj) < 1 for adj in adjustments):
            print("--- アルゴリズムが収束しました。---")
            return s

        s = new_s
        print(f"  更新後の初期在庫量: {s}")

    print("--- 最大反復回数に到達しました。---")
    return s


def solve_mip(initial_inventory_list_arg):
    """PuLPを用いてMIPを解く関数"""
    
    # 修正：ここではread_csvを呼び出さない
    品番数 = len(品番リスト)
    
    # モデルの定義
    model = pulp.LpProblem("ProductionScheduling", pulp.LpMinimize)
    
    # インデックスの定義
    品目 = range(品番数)
    期間_index = range(期間)
    
    # 決定変数の定義
    Production = pulp.LpVariable.dicts("Production", (品目, 期間_index), lowBound=0, cat='Continuous')
    Inventory = pulp.LpVariable.dicts("Inventory", (品目, 期間_index), lowBound=0, cat='Continuous')
    Shortage = pulp.LpVariable.dicts("Shortage", (品目, 期間_index), lowBound=0, cat='Continuous')
    IsProduced = pulp.LpVariable.dicts("IsProduced", (品目, 期間_index), cat='Binary')
    WorkTime = pulp.LpVariable.dicts("WorkTime", 期間_index, lowBound=0, cat='Continuous')
    Overtime = pulp.LpVariable.dicts("Overtime", 期間_index, lowBound=0, cat='Continuous')

    # 目的関数
    total_cost = pulp.lpSum(
        在庫コスト単価 * Inventory[i][t] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        残業コスト単価 * Overtime[t] for t in 期間_index
    ) + pulp.lpSum(
        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index
    )
    
    model += total_cost, "Total Cost"

    # 制約条件
    bigM = 1000000

    for i in 品目:
        for t in 期間_index:
            if t == 0:
                # 修正：引数で受け取った初期在庫リストを使用
                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]
            else:
                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]
            
            model += Production[i][t] <= bigM * IsProduced[i][t]

    for t in 期間_index:
        model += WorkTime[t] == pulp.lpSum(
            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]
            for i in 品目
        )
        
        model += WorkTime[t] <= 定時 + Overtime[t]
        model += WorkTime[t] <= 定時 + 最大残業時間
        model += Overtime[t] >= WorkTime[t] - 定時
        model += Overtime[t] >= 0

    # Solverの設定 - 利用可能なソルバーを順番に試す
    try:
        # まずGUROBIを試す
        solver = pulp.GUROBI(msg=False)
        model.solve(solver)
    except:
        try:
            # GUROBIが利用できない場合はCBC（デフォルト）を使用
            solver = pulp.PULP_CBC_CMD(msg=False)
            model.solve(solver)
        except:
            # CBCも利用できない場合はデフォルトソルバーを使用
            model.solve()
    
    if pulp.LpStatus[model.status] == 'Optimal':
        production_schedule = [[0] * 期間 for _ in range(品番数)]
        for i in 品目:
            for t in 期間_index:
                production_schedule[i][t] = pulp.value(Production[i][t])

        return production_schedule, pulp.value(model.objective)

    return None, None


def evaluate(solution, current_initial_inventory):
    """総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数"""
    global 品番数, 期間

    # 最小化コスト
    total_inventory_cost = 0
    total_overtime_cost = 0
    total_setup_cost = 0
    total_shipment_delay_cost = 0

    inventory = current_initial_inventory[:]
    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）
    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）

    inventory_history = [[] for _ in range(品番数)]

    for t in range(期間):
        daily_time = 0
        daily_setup_count = 0

        for i in range(品番数):
            production = solution[t][i]

            # 生産がある場合に段替えをする
            if production > 0:
                daily_setup_count += 1
                setup_time = 30
            else:
                setup_time = 0

            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
            daily_time += production_time + setup_time

            inventory[i] += production - 出荷数リスト[i]
            inventory_history[i].append(inventory[i])

            # 出荷遅れコスト
            if inventory[i] < 0:
                shortage_amount = abs(inventory[i])
                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount

            # 在庫コスト
            if inventory[i] > 0:
                total_inventory_cost += 在庫コスト単価 * inventory[i]

        # 段替えコスト
        total_setup_cost += 段替えコスト単価 * daily_setup_count

        # 残業コスト
        if daily_time > daily_regular_time:
            overtime = daily_time - daily_regular_time
            total_overtime_cost += 残業コスト単価 * overtime

        # 最大稼働時間超過ペナルティ（違反する場合はコストに加算）
        if daily_time > daily_regular_time + max_daily_overtime:
            work_time_penalty = (daily_time - (daily_regular_time + max_daily_overtime)) * (残業コスト単価 * 1000000)
            total_overtime_cost += work_time_penalty

    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost

    return total_cost, inventory_history


def generate_initial_solution(current_initial_inventory):
    """初期解を生成する関数"""
    solution = []
    temp_inventory = current_initial_inventory[:]
    max_daily_work_time = (8 + 2) * 60 * 2

    for t in range(期間):
        daily_productions = [0] * 品番数
        daily_time = 0

        priorities = []
        for i in range(品番数):
            shortage_estimate = max(0, 出荷数リスト[i] - temp_inventory[i])
            priorities.append((shortage_estimate, i))

        priorities.sort(key=lambda x: x[0], reverse=True)

        for shortage_estimate, i in priorities:
            if shortage_estimate > 0:
                setup_time = 30
                remaining_time = max_daily_work_time - daily_time

                if remaining_time > setup_time:
                    cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]
                    if cycle_time_per_unit > 0:
                        max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)

                        if max_producible_by_time > 0:
                            target_production = shortage_estimate + random.randint(0, 50)
                            production = min(target_production, max_producible_by_time)

                            daily_productions[i] = production
                            daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]

        solution.append(daily_productions)

        for i in range(品番数):
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]

    return solution


def get_neighbors(current_solution):
    """近傍解を生成する関数"""
    neighbors = []

    # 2つの生産量を入れ替える
    for _ in range(5):
        neighbor = copy.deepcopy(current_solution)
        t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)
        i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)

        if (t1, i1) != (t2, i2):
            neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]
            neighbors.append(neighbor)

    # 特定の生産量を増減させる
    for _ in range(5):
        neighbor = copy.deepcopy(current_solution)
        t = random.randint(0, 期間 - 1)
        i = random.randint(0, 品番数 - 1)

        change = random.randint(-50, 50)
        neighbor[t][i] = max(0, neighbor[t][i] + change)
        neighbors.append(neighbor)

    return neighbors


def local_search(initial_solution, current_initial_inventory):
    """ローカルサーチを実行する関数"""
    current_solution = initial_solution
    current_cost, _ = evaluate(current_solution, current_initial_inventory)

    while True:
        neighbors = get_neighbors(current_solution)
        best_neighbor = None
        best_neighbor_cost = float('inf')

        for neighbor in neighbors:
            neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)
            if neighbor_cost < best_neighbor_cost:
                best_neighbor = neighbor
                best_neighbor_cost = neighbor_cost

        if best_neighbor_cost < current_cost:
            current_solution = best_neighbor
            current_cost = best_neighbor_cost
        else:
            break

    return current_solution, current_cost


def multi_start_local_search(num_starts, current_initial_inventory, verbose=True):
    """多スタートローカルサーチを実行する関数"""
    best_solution_overall = None
    best_cost_overall = float('inf')

    for i in range(num_starts):
        if verbose:
            print(f"--- Start {i+1}/{num_starts} ---")

        initial_solution = generate_initial_solution(current_initial_inventory)

        local_optimal_solution, local_optimal_cost = local_search(initial_solution, current_initial_inventory)

        if local_optimal_cost < best_cost_overall:
            best_cost_overall = local_optimal_cost
            best_solution_overall = local_optimal_solution
            if verbose:
                print(f"  New best solution found with total cost: {best_cost_overall:.2f}")

    return best_solution_overall, best_cost_overall


def run_comparison(csv_file='data.csv', num_starts=30, verbose=True):
    """両方の最適化手法を実行して比較する関数"""
    global 品番数, 初期在庫量リスト

    print("=" * 80)
    print("生産スケジューリング最適化手法比較システム")
    print("=" * 80)

    # データの読み込み
    result = read_csv(csv_file)
    if result[0] is None:
        print("CSVファイルの読み込みに失敗しました")
        return None

    品番数 = len(品番リスト)

    # 初期在庫量を調整
    print(f"\n--- 元の初期在庫 ---")
    for i, part in enumerate(品番リスト):
        print(f"製品 {part}: {初期在庫量リスト[i]}")

    adjusted_initial_inventory = adjust_initial_inventory(
        初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価,
        num_simulations=50, max_iterations=5
    )

    print(f"\n--- 調整後の初期在庫 ---")
    for i, part in enumerate(品番リスト):
        print(f"製品 {part}: {adjusted_initial_inventory[i]}")

    # 結果を格納する辞書
    results = {
        'mip': {'time': 0, 'objective': 0, 'solution': None, 'status': 'Failed'},
        'local_search': {'time': 0, 'objective': 0, 'solution': None, 'status': 'Failed'}
    }

    print("\n" + "=" * 80)
    print("1. 混合整数計画法 (MIP) による最適化")
    print("=" * 80)

    # MIPの実行
    start_time = time.time()
    try:
        mip_solution, mip_cost = solve_mip(adjusted_initial_inventory)
        mip_time = time.time() - start_time

        if mip_solution is not None:
            results['mip']['time'] = mip_time
            results['mip']['objective'] = mip_cost
            results['mip']['solution'] = mip_solution
            results['mip']['status'] = 'Optimal'

            print(f"ステータス: 最適解発見")
            print(f"総コスト: {mip_cost:.2f}")
            print(f"計算時間: {mip_time:.4f} 秒")
        else:
            print("MIPで解が見つかりませんでした")
    except Exception as e:
        print(f"MIP実行中にエラーが発生しました: {e}")

    print("\n" + "=" * 80)
    print("2. 多スタートローカルサーチによる最適化")
    print("=" * 80)

    # 多スタートローカルサーチの実行
    start_time = time.time()
    try:
        ls_solution, ls_cost = multi_start_local_search(
            num_starts, adjusted_initial_inventory, verbose=verbose
        )
        ls_time = time.time() - start_time

        if ls_solution is not None:
            results['local_search']['time'] = ls_time
            results['local_search']['objective'] = ls_cost
            results['local_search']['solution'] = ls_solution
            results['local_search']['status'] = 'Local Optimum'

            print(f"\nステータス: 局所最適解発見")
            print(f"総コスト: {ls_cost:.2f}")
            print(f"計算時間: {ls_time:.4f} 秒")
        else:
            print("多スタートローカルサーチで解が見つかりませんでした")
    except Exception as e:
        print(f"多スタートローカルサーチ実行中にエラーが発生しました: {e}")

    return results, adjusted_initial_inventory


def print_comparison_results(results):
    """比較結果を表示する関数"""
    print("\n" + "=" * 80)
    print("最適化手法比較結果")
    print("=" * 80)

    # 結果テーブルの作成
    comparison_data = {
        '手法': ['混合整数計画法 (MIP)', '多スタートローカルサーチ'],
        'ステータス': [results['mip']['status'], results['local_search']['status']],
        '目的関数値': [f"{results['mip']['objective']:.2f}" if results['mip']['objective'] else 'N/A',
                    f"{results['local_search']['objective']:.2f}" if results['local_search']['objective'] else 'N/A'],
        '計算時間 (秒)': [f"{results['mip']['time']:.4f}" if results['mip']['time'] else 'N/A',
                       f"{results['local_search']['time']:.4f}" if results['local_search']['time'] else 'N/A']
    }

    df_comparison = pd.DataFrame(comparison_data)
    print(df_comparison.to_string(index=False))

    # 詳細分析
    if results['mip']['objective'] and results['local_search']['objective']:
        mip_obj = results['mip']['objective']
        ls_obj = results['local_search']['objective']

        print(f"\n--- 詳細分析 ---")
        print(f"目的関数値の差: {abs(mip_obj - ls_obj):.2f}")

        if mip_obj < ls_obj:
            improvement = ((ls_obj - mip_obj) / ls_obj) * 100
            print(f"MIPの方が {improvement:.2f}% 良い解を発見")
            print(f"MIPの解はローカルサーチの解より {ls_obj/mip_obj:.2f} 倍良い")
        elif ls_obj < mip_obj:
            improvement = ((mip_obj - ls_obj) / mip_obj) * 100
            print(f"ローカルサーチの方が {improvement:.2f}% 良い解を発見")
        else:
            print("両手法とも同じ目的関数値を達成")

        # 計算時間の比較
        if results['mip']['time'] and results['local_search']['time']:
            mip_time = results['mip']['time']
            ls_time = results['local_search']['time']

            if mip_time < ls_time:
                speedup = ls_time / mip_time
                print(f"MIPの方が {speedup:.2f} 倍高速")
            else:
                speedup = mip_time / ls_time
                print(f"ローカルサーチの方が {speedup:.2f} 倍高速")


def plot_comparison_results(results):
    """比較結果をグラフで表示する関数"""
    if not (results['mip']['objective'] and results['local_search']['objective']):
        print("プロット用のデータが不足しています")
        return

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    # 目的関数値の比較
    methods = ['MIP', 'Local Search']
    objectives = [results['mip']['objective'], results['local_search']['objective']]
    colors = ['blue', 'red']

    ax1.bar(methods, objectives, color=colors, alpha=0.7)
    ax1.set_ylabel('目的関数値')
    ax1.set_title('目的関数値の比較')
    ax1.grid(True, alpha=0.3)

    # 値をバーの上に表示
    for i, v in enumerate(objectives):
        ax1.text(i, v + max(objectives) * 0.01, f'{v:.2f}', ha='center', va='bottom')

    # 計算時間の比較
    times = [results['mip']['time'], results['local_search']['time']]

    ax2.bar(methods, times, color=colors, alpha=0.7)
    ax2.set_ylabel('計算時間 (秒)')
    ax2.set_title('計算時間の比較')
    ax2.grid(True, alpha=0.3)

    # 値をバーの上に表示
    for i, v in enumerate(times):
        ax2.text(i, v + max(times) * 0.01, f'{v:.4f}', ha='center', va='bottom')

    plt.tight_layout()
    plt.show()


def process_all_data_files(data_folder='data', num_starts=30, verbose=False):
    """データフォルダ内の全CSVファイルを処理して比較する関数"""
    import os
    import glob

    # データフォルダ内のCSVファイルを取得
    csv_files = glob.glob(os.path.join(data_folder, '*.csv'))
    csv_files.sort()  # ファイル名でソート

    if not csv_files:
        print(f"データフォルダ '{data_folder}' にCSVファイルが見つかりません")
        return None

    print("=" * 80)
    print("全データファイル処理開始")
    print("=" * 80)
    print(f"処理対象ファイル: {len(csv_files)}個")
    for file in csv_files:
        print(f"  - {os.path.basename(file)}")
    print()

    # 結果を格納する辞書
    all_results = {}
    summary_data = {
        'files': [],
        'mip_objective': [],
        'mip_time': [],
        'ls_objective': [],
        'ls_time': [],
        'mip_status': [],
        'ls_status': []
    }

    # 各ファイルを処理
    for i, csv_file in enumerate(csv_files):
        file_name = os.path.basename(csv_file)
        print(f"\n{'='*60}")
        print(f"処理中: {file_name} ({i+1}/{len(csv_files)})")
        print(f"{'='*60}")

        try:
            # 個別ファイルの最適化実行
            results, adjusted_inventory = run_comparison(
                csv_file=csv_file,
                num_starts=num_starts,
                verbose=verbose
            )

            if results:
                all_results[file_name] = {
                    'results': results,
                    'adjusted_inventory': adjusted_inventory
                }

                # サマリーデータに追加
                summary_data['files'].append(file_name)
                summary_data['mip_objective'].append(results['mip']['objective'])
                summary_data['mip_time'].append(results['mip']['time'])
                summary_data['ls_objective'].append(results['local_search']['objective'])
                summary_data['ls_time'].append(results['local_search']['time'])
                summary_data['mip_status'].append(results['mip']['status'])
                summary_data['ls_status'].append(results['local_search']['status'])

                print(f"\n--- {file_name} 結果サマリー ---")
                print(f"MIP: {results['mip']['objective']:.2f} (時間: {results['mip']['time']:.4f}秒)")
                print(f"LS:  {results['local_search']['objective']:.2f} (時間: {results['local_search']['time']:.4f}秒)")

            else:
                print(f"ファイル {file_name} の処理に失敗しました")

        except Exception as e:
            print(f"ファイル {file_name} の処理中にエラーが発生しました: {e}")

    return all_results, summary_data


def plot_and_save_summary_results(summary_data, save_path='comparison_summary'):
    """全結果のサマリープロットを作成・保存する関数"""
    if not summary_data['files']:
        print("プロット用のデータがありません")
        return

    # 日本語フォント設定（警告を避けるため）
    plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'Hiragino Sans']

    # 図のサイズを設定
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    files = summary_data['files']
    x_pos = range(len(files))

    # 1. 目的関数値の比較
    ax1.bar([x - 0.2 for x in x_pos], summary_data['mip_objective'],
            width=0.4, label='MIP', alpha=0.8, color='blue')
    ax1.bar([x + 0.2 for x in x_pos], summary_data['ls_objective'],
            width=0.4, label='Local Search', alpha=0.8, color='red')
    ax1.set_xlabel('Data Files')
    ax1.set_ylabel('Objective Value')
    ax1.set_title('Objective Value Comparison')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(files, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 計算時間の比較
    ax2.bar([x - 0.2 for x in x_pos], summary_data['mip_time'],
            width=0.4, label='MIP', alpha=0.8, color='blue')
    ax2.bar([x + 0.2 for x in x_pos], summary_data['ls_time'],
            width=0.4, label='Local Search', alpha=0.8, color='red')
    ax2.set_xlabel('Data Files')
    ax2.set_ylabel('Calculation Time (seconds)')
    ax2.set_title('Calculation Time Comparison')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(files, rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 総目的関数値（合計）
    total_mip_obj = sum(summary_data['mip_objective'])
    total_ls_obj = sum(summary_data['ls_objective'])
    ax3.bar(['MIP', 'Local Search'], [total_mip_obj, total_ls_obj],
            color=['blue', 'red'], alpha=0.8)
    ax3.set_ylabel('Total Objective Value')
    ax3.set_title('Total Objective Value Sum')
    ax3.grid(True, alpha=0.3)

    # 値をバーの上に表示
    ax3.text(0, total_mip_obj + total_mip_obj*0.01, f'{total_mip_obj:.0f}',
             ha='center', va='bottom')
    ax3.text(1, total_ls_obj + total_ls_obj*0.01, f'{total_ls_obj:.0f}',
             ha='center', va='bottom')

    # 4. 総計算時間（合計）
    total_mip_time = sum(summary_data['mip_time'])
    total_ls_time = sum(summary_data['ls_time'])
    ax4.bar(['MIP', 'Local Search'], [total_mip_time, total_ls_time],
            color=['blue', 'red'], alpha=0.8)
    ax4.set_ylabel('Total Calculation Time (seconds)')
    ax4.set_title('Total Calculation Time Sum')
    ax4.grid(True, alpha=0.3)

    # 値をバーの上に表示
    ax4.text(0, total_mip_time + total_mip_time*0.01, f'{total_mip_time:.4f}',
             ha='center', va='bottom')
    ax4.text(1, total_ls_time + total_ls_time*0.01, f'{total_ls_time:.4f}',
             ha='center', va='bottom')

    plt.tight_layout()

    # 画像として保存
    plt.savefig(f'{save_path}.png', dpi=300, bbox_inches='tight')
    plt.savefig(f'{save_path}.pdf', bbox_inches='tight')
    print(f"\nプロットを保存しました: {save_path}.png, {save_path}.pdf")

    plt.show()


def print_summary_table(summary_data):
    """サマリーテーブルを表示する関数"""
    print("\n" + "=" * 100)
    print("全データファイル処理結果サマリー")
    print("=" * 100)

    # ヘッダー
    print(f"{'ファイル名':<15} {'MIP目的関数値':<15} {'MIP時間(秒)':<12} {'LS目的関数値':<15} {'LS時間(秒)':<12} {'MIP優位性':<10}")
    print("-" * 100)

    total_mip_obj = 0
    total_mip_time = 0
    total_ls_obj = 0
    total_ls_time = 0

    for i, file in enumerate(summary_data['files']):
        mip_obj = summary_data['mip_objective'][i]
        mip_time = summary_data['mip_time'][i]
        ls_obj = summary_data['ls_objective'][i]
        ls_time = summary_data['ls_time'][i]

        # MIPの優位性を計算
        if ls_obj > 0:
            improvement = ((ls_obj - mip_obj) / ls_obj) * 100
            improvement_str = f"{improvement:.1f}%"
        else:
            improvement_str = "N/A"

        print(f"{file:<15} {mip_obj:<15.0f} {mip_time:<12.4f} {ls_obj:<15.0f} {ls_time:<12.4f} {improvement_str:<10}")

        total_mip_obj += mip_obj
        total_mip_time += mip_time
        total_ls_obj += ls_obj
        total_ls_time += ls_time

    print("-" * 100)
    print(f"{'合計':<15} {total_mip_obj:<15.0f} {total_mip_time:<12.4f} {total_ls_obj:<15.0f} {total_ls_time:<12.4f}")

    # 全体の改善率
    if total_ls_obj > 0 and total_mip_obj > 0:
        total_improvement = ((total_ls_obj - total_mip_obj) / total_ls_obj) * 100
        print(f"\n全体でのMIP優位性: {total_improvement:.2f}%")
        print(f"MIPの解はローカルサーチより {total_ls_obj/total_mip_obj:.2f} 倍良い")
    elif total_mip_obj == 0 and total_ls_obj > 0:
        print(f"\nMIPソルバーが利用できないため、ローカルサーチのみの結果です")
        print(f"ローカルサーチの総目的関数値: {total_ls_obj:.0f}")

    if total_mip_time > 0 and total_ls_time > 0:
        speed_ratio = total_ls_time / total_mip_time
        if speed_ratio < 1:
            print(f"ローカルサーチの方が {1/speed_ratio:.2f} 倍高速")
        else:
            print(f"MIPの方が {speed_ratio:.2f} 倍高速")
    elif total_mip_time == 0 and total_ls_time > 0:
        print(f"ローカルサーチの総計算時間: {total_ls_time:.4f} 秒")


def main():
    print("生産スケジューリング最適化手法比較ツール")
    print("Mixed Integer Programming vs Multi-Start Local Search")
    print("全データファイル一括処理モード")

    # 全データファイルを処理
    all_results, summary_data = process_all_data_files(
        data_folder='data',
        num_starts=30,
        verbose=False  # 個別の詳細出力は無効化
    )

    if all_results and summary_data['files']:
        # サマリーテーブル表示
        print_summary_table(summary_data)

        # サマリープロット作成・保存
        plot_and_save_summary_results(summary_data, 'all_data_comparison_summary')

        # CSVファイルとして結果を保存
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv('all_data_results_summary.csv', index=False)
        print(f"\n結果をCSVファイルに保存しました: all_data_results_summary.csv")

    else:
        print("処理できるデータファイルがありませんでした")


if __name__ == "__main__":
    main()
